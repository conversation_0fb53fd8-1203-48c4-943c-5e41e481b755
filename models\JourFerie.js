// ===================================================================
// MODÈLE JOUR FÉRIÉ - JOURS FÉRIÉS ET CONGÉS OFFICIELS
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const JourFerie = sequelize.define('JourFerie', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom_ferie: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le nom du jour férié ne peut pas être vide'
        },
        len: {
          args: [3, 200],
          msg: 'Le nom du jour férié doit contenir entre 3 et 200 caractères'
        }
      }
    },
    date_ferie: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: {
          msg: 'Format de date invalide'
        }
      }
    },
    type_ferie: {
      type: DataTypes.ENUM('National', 'Religieux', 'Militaire', 'Regional'),
      defaultValue: 'National',
      validate: {
        isIn: {
          args: [['National', 'Religieux', 'Militaire', 'Regional']],
          msg: 'Type de jour férié invalide'
        }
      }
    },
    recurrent: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      validate: {
        isBoolean: {
          msg: 'La récurrence doit être vraie ou fausse'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'La description ne peut pas dépasser 1000 caractères'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'jours_feries',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['date_ferie']
      },
      {
        fields: ['type_ferie']
      },
      {
        fields: ['recurrent']
      },
      {
        unique: true,
        fields: ['nom_ferie', 'date_ferie']
      }
    ],
    hooks: {
      beforeValidate: (jourFerie) => {
        // Nettoyer et formater les données
        if (jourFerie.nom_ferie) {
          jourFerie.nom_ferie = jourFerie.nom_ferie.trim();
        }
        if (jourFerie.description) {
          jourFerie.description = jourFerie.description.trim();
        }
      }
    }
  });

  // Méthodes d'instance
  JourFerie.prototype.estRecurrent = function() {
    return this.recurrent === true;
  };

  JourFerie.prototype.estNational = function() {
    return this.type_ferie === 'National';
  };

  JourFerie.prototype.estReligieux = function() {
    return this.type_ferie === 'Religieux';
  };

  JourFerie.prototype.estMilitaire = function() {
    return this.type_ferie === 'Militaire';
  };

  JourFerie.prototype.estRegional = function() {
    return this.type_ferie === 'Regional';
  };

  JourFerie.prototype.estAujourdhui = function() {
    const aujourd_hui = new Date().toISOString().split('T')[0];
    return this.date_ferie === aujourd_hui;
  };

  JourFerie.prototype.estDansLeFutur = function() {
    const aujourd_hui = new Date().toISOString().split('T')[0];
    return this.date_ferie > aujourd_hui;
  };

  JourFerie.prototype.estDansLePasse = function() {
    const aujourd_hui = new Date().toISOString().split('T')[0];
    return this.date_ferie < aujourd_hui;
  };

  JourFerie.prototype.getJoursRestants = function() {
    if (!this.estDansLeFutur()) return 0;
    
    const aujourd_hui = new Date();
    const dateFerie = new Date(this.date_ferie);
    const diffTime = dateFerie - aujourd_hui;
    
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  JourFerie.prototype.getTypeLibelle = function() {
    const types = {
      'National': 'Fête Nationale',
      'Religieux': 'Fête Religieuse',
      'Militaire': 'Fête Militaire',
      'Regional': 'Fête Régionale'
    };
    
    return types[this.type_ferie] || this.type_ferie;
  };

  JourFerie.prototype.getTypeCouleur = function() {
    const couleurs = {
      'National': '#dc3545',
      'Religieux': '#28a745',
      'Militaire': '#007bff',
      'Regional': '#ffc107'
    };
    
    return couleurs[this.type_ferie] || '#6c757d';
  };

  // Méthodes de classe
  JourFerie.getJoursFeriesAnnee = async function(annee) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: sequelize.where(
        sequelize.fn('YEAR', sequelize.col('date_ferie')),
        annee
      ),
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getJoursFeriesMois = async function(annee, mois) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        [Op.and]: [
          sequelize.where(sequelize.fn('YEAR', sequelize.col('date_ferie')), annee),
          sequelize.where(sequelize.fn('MONTH', sequelize.col('date_ferie')), mois)
        ]
      },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getJoursFeriesPeriode = async function(dateDebut, dateFin) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        date_ferie: {
          [Op.between]: [dateDebut, dateFin]
        }
      },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getProchainJourFerie = async function() {
    const { Op } = sequelize.Sequelize;
    const aujourd_hui = new Date().toISOString().split('T')[0];
    
    return await this.findOne({
      where: {
        date_ferie: {
          [Op.gt]: aujourd_hui
        }
      },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getJoursFeriesParType = async function(typeFerie) {
    return await this.findAll({
      where: { type_ferie: typeFerie },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.estJourFerie = async function(date) {
    const jourFerie = await this.findOne({
      where: { date_ferie: date }
    });
    
    return jourFerie !== null;
  };

  JourFerie.compterJoursFeriesPeriode = async function(dateDebut, dateFin) {
    const { Op } = sequelize.Sequelize;
    
    return await this.count({
      where: {
        date_ferie: {
          [Op.between]: [dateDebut, dateFin]
        }
      }
    });
  };

  JourFerie.getJoursFeriesRecurrents = async function() {
    return await this.findAll({
      where: { recurrent: true },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getJoursFeriesNonRecurrents = async function() {
    return await this.findAll({
      where: { recurrent: false },
      order: [['date_ferie', 'ASC']]
    });
  };

  JourFerie.getStatistiquesParType = async function() {
    return await this.findAll({
      attributes: [
        'type_ferie',
        [sequelize.fn('COUNT', sequelize.col('id')), 'nombre'],
        [sequelize.fn('COUNT', 
          sequelize.literal('CASE WHEN recurrent = true THEN 1 END')
        ), 'recurrents']
      ],
      group: ['type_ferie'],
      order: [['type_ferie', 'ASC']]
    });
  };

  JourFerie.rechercherParNom = async function(terme) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        nom_ferie: {
          [Op.like]: `%${terme}%`
        }
      },
      order: [['date_ferie', 'ASC']]
    });
  };

  // Scopes
  JourFerie.addScope('nationaux', {
    where: { type_ferie: 'National' }
  });

  JourFerie.addScope('religieux', {
    where: { type_ferie: 'Religieux' }
  });

  JourFerie.addScope('militaires', {
    where: { type_ferie: 'Militaire' }
  });

  JourFerie.addScope('regionaux', {
    where: { type_ferie: 'Regional' }
  });

  JourFerie.addScope('recurrents', {
    where: { recurrent: true }
  });

  JourFerie.addScope('nonRecurrents', {
    where: { recurrent: false }
  });

  JourFerie.addScope('futurs', {
    where: {
      date_ferie: {
        [sequelize.Sequelize.Op.gt]: new Date().toISOString().split('T')[0]
      }
    }
  });

  JourFerie.addScope('passes', {
    where: {
      date_ferie: {
        [sequelize.Sequelize.Op.lt]: new Date().toISOString().split('T')[0]
      }
    }
  });

  return JourFerie;
};
