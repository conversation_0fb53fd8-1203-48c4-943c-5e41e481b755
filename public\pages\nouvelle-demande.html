<!-- ===================================================================
     PAGE NOUVELLE DEMANDE DE CONGÉ
     =================================================================== -->

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-plus-circle text-primary me-2"></i>
                        Nouvelle Demande de Congé
                    </h1>
                    <p class="text-muted">Remplissez le formulaire pour soumettre votre demande</p>
                </div>
                <button class="btn btn-outline-secondary" onclick="showPage('demandes')">
                    <i class="fas fa-arrow-left me-2"></i>Retour aux demandes
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-form me-2"></i>Formulaire de Demande
                    </h6>
                </div>
                <div class="card-body">
                    <form id="nouvelle-demande-form" class="ajax-form" data-action="/demandes" data-method="POST">
                        <!-- Type de congé -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="type_conge_id" class="form-label">Type de congé <span class="text-danger">*</span></label>
                                <select class="form-select" id="type_conge_id" name="type_conge_id" required>
                                    <option value="">Sélectionnez un type</option>
                                    <option value="1">Congé Annuel</option>
                                    <option value="2">Congé de Maladie</option>
                                    <option value="3">Congé Exceptionnel</option>
                                    <option value="4">Congé de Maternité</option>
                                    <option value="5">Congé de Paternité</option>
                                    <option value="6">Congé de Décès</option>
                                    <option value="7">Congé de Mariage</option>
                                    <option value="8">Permission de Sortie</option>
                                </select>
                                <div class="form-text">Choisissez le type de congé approprié</div>
                            </div>
                            <div class="col-md-6">
                                <label for="remplacant_id" class="form-label">Remplaçant</label>
                                <select class="form-select" id="remplacant_id" name="remplacant_id">
                                    <option value="">Aucun remplaçant</option>
                                    <option value="2">Lieutenant BENALI Ahmed</option>
                                    <option value="3">Sergent MEZIANE Omar</option>
                                    <option value="4">Caporal KADDOUR Fatima</option>
                                </select>
                                <div class="form-text">Personne qui vous remplacera pendant votre absence</div>
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="date_debut" class="form-label">Date de début <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_debut" name="date_debut" required>
                                <div class="form-text">Date de début du congé</div>
                            </div>
                            <div class="col-md-6">
                                <label for="date_fin" class="form-label">Date de fin <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_fin" name="date_fin" required>
                                <div class="form-text">Date de fin du congé</div>
                            </div>
                        </div>

                        <!-- Durée calculée -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Durée calculée</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="nombre_jours" name="nombre_jours" readonly>
                                    <span class="input-group-text">jour(s)</span>
                                </div>
                                <div class="form-text">Calculé automatiquement selon les dates</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Congés restants</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" value="23" readonly>
                                    <span class="input-group-text">jour(s)</span>
                                </div>
                                <div class="form-text">Votre solde de congés annuels</div>
                            </div>
                        </div>

                        <!-- Motif -->
                        <div class="mb-3">
                            <label for="motif" class="form-label">Motif <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="motif" name="motif" rows="3" required 
                                placeholder="Décrivez le motif de votre demande de congé..."></textarea>
                            <div class="form-text">Expliquez brièvement la raison de votre demande</div>
                        </div>

                        <!-- Coordonnées pendant le congé -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="adresse_conge" class="form-label">Adresse pendant le congé</label>
                                <textarea class="form-control" id="adresse_conge" name="adresse_conge" rows="2"
                                    placeholder="Votre adresse pendant la période de congé..."></textarea>
                            </div>
                            <div class="col-md-4">
                                <label for="telephone_conge" class="form-label">Téléphone de contact</label>
                                <input type="tel" class="form-control" id="telephone_conge" name="telephone_conge"
                                    placeholder="+213 555 123 456">
                            </div>
                        </div>

                        <!-- Document justificatif -->
                        <div class="mb-3">
                            <label for="document_justificatif" class="form-label">Document justificatif</label>
                            <input type="file" class="form-control" id="document_justificatif" name="document_justificatif"
                                accept=".pdf,.jpg,.jpeg,.png">
                            <div class="form-text">
                                <i class="fas fa-info-circle text-info me-1"></i>
                                Requis pour certains types de congés (maladie, mariage, décès, etc.)
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                                    <i class="fas fa-save me-2"></i>Sauvegarder brouillon
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Soumettre la demande
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar avec informations -->
        <div class="col-lg-4">
            <!-- Informations sur les congés -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Informations
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Congés annuels:</span>
                            <span class="fw-bold">30 jours</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Congés utilisés:</span>
                            <span class="fw-bold text-warning">7 jours</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Congés restants:</span>
                            <span class="fw-bold text-success">23 jours</span>
                        </div>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-warning" style="width: 23%"></div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Année en cours: 2024
                    </small>
                </div>
            </div>

            <!-- Règles et conseils -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exclamation-triangle me-2"></i>Règles importantes
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Soumettre la demande au moins 7 jours à l'avance</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Maximum 21 jours consécutifs pour les congés annuels</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Document justificatif obligatoire pour certains types</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Désigner un remplaçant si nécessaire</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Prochains jours fériés -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-star me-2"></i>Prochains jours fériés
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary text-white rounded text-center me-3" style="width: 40px; height: 40px; line-height: 40px;">
                            <small class="fw-bold">01</small>
                        </div>
                        <div>
                            <div class="fw-semibold">Nouvel An</div>
                            <small class="text-muted">01 Janvier 2024</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-success text-white rounded text-center me-3" style="width: 40px; height: 40px; line-height: 40px;">
                            <small class="fw-bold">01</small>
                        </div>
                        <div>
                            <div class="fw-semibold">Fête du Travail</div>
                            <small class="text-muted">01 Mai 2024</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Calcul automatique de la durée
document.addEventListener('DOMContentLoaded', function() {
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    const nombreJours = document.getElementById('nombre_jours');
    
    function calculateDuration() {
        if (dateDebut.value && dateFin.value) {
            const debut = new Date(dateDebut.value);
            const fin = new Date(dateFin.value);
            
            if (fin >= debut) {
                const diffTime = Math.abs(fin - debut);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                nombreJours.value = diffDays;
            } else {
                nombreJours.value = '';
                NotificationManager.warning('La date de fin doit être postérieure à la date de début');
            }
        }
    }
    
    dateDebut.addEventListener('change', calculateDuration);
    dateFin.addEventListener('change', calculateDuration);
    
    // Définir la date minimum à aujourd'hui
    const today = new Date().toISOString().split('T')[0];
    dateDebut.min = today;
    dateFin.min = today;
});

// Réinitialiser le formulaire
function resetForm() {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
        document.getElementById('nouvelle-demande-form').reset();
        document.getElementById('nombre_jours').value = '';
    }
}

// Sauvegarder en brouillon
function saveDraft() {
    const formData = new FormData(document.getElementById('nouvelle-demande-form'));
    const data = Object.fromEntries(formData.entries());
    
    localStorage.setItem('demande_draft', JSON.stringify(data));
    NotificationManager.success('Brouillon sauvegardé');
}

// Charger un brouillon
function loadDraft() {
    const draft = localStorage.getItem('demande_draft');
    if (draft) {
        const data = JSON.parse(draft);
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = data[key];
            }
        });
        NotificationManager.info('Brouillon chargé');
    }
}
</script>
