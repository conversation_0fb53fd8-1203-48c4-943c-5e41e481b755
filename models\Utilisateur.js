// ===================================================================
// MODÈLE UTILISATEUR - PERSONNEL MILITAIRE
// ===================================================================

const bcrypt = require('bcrypt');

module.exports = (sequelize, DataTypes) => {
  const Utilisateur = sequelize.define('Utilisateur', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    matricule: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Le matricule ne peut pas être vide'
        },
        len: {
          args: [3, 20],
          msg: 'Le matricule doit contenir entre 3 et 20 caractères'
        },
        isAlphanumeric: {
          msg: 'Le matricule ne peut contenir que des lettres et des chiffres'
        }
      }
    },
    nom: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le nom ne peut pas être vide'
        },
        len: {
          args: [2, 100],
          msg: 'Le nom doit contenir entre 2 et 100 caractères'
        },
        is: {
          args: /^[a-zA-ZÀ-ÿ\s\-']+$/,
          msg: 'Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes'
        }
      }
    },
    prenom: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le prénom ne peut pas être vide'
        },
        len: {
          args: [2, 100],
          msg: 'Le prénom doit contenir entre 2 et 100 caractères'
        },
        is: {
          args: /^[a-zA-ZÀ-ÿ\s\-']+$/,
          msg: 'Le prénom ne peut contenir que des lettres, espaces, tirets et apostrophes'
        }
      }
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: {
          msg: 'Format d\'email invalide'
        },
        notEmpty: {
          msg: 'L\'email ne peut pas être vide'
        }
      }
    },
    mot_de_passe: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le mot de passe ne peut pas être vide'
        },
        len: {
          args: [6, 255],
          msg: 'Le mot de passe doit contenir au moins 6 caractères'
        }
      }
    },
    grade_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'grades',
        key: 'id'
      }
    },
    unite_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'unites',
        key: 'id'
      }
    },
    superieur_direct_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'utilisateurs',
        key: 'id'
      },
      validate: {
        async notSelfReference(value) {
          if (value === this.id) {
            throw new Error('Un utilisateur ne peut pas être son propre supérieur');
          }
        }
      }
    },
    date_naissance: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Format de date invalide'
        },
        isBefore: {
          args: new Date().toISOString().split('T')[0],
          msg: 'La date de naissance ne peut pas être dans le futur'
        }
      }
    },
    lieu_naissance: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: 'Le lieu de naissance ne peut pas dépasser 100 caractères'
        }
      }
    },
    date_incorporation: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Format de date invalide'
        },
        isBefore: {
          args: new Date().toISOString().split('T')[0],
          msg: 'La date d\'incorporation ne peut pas être dans le futur'
        }
      }
    },
    telephone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[0-9\-\s\(\)]+$/,
          msg: 'Format de téléphone invalide'
        }
      }
    },
    adresse: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    statut: {
      type: DataTypes.ENUM('Actif', 'Inactif', 'Suspendu', 'Retraité'),
      defaultValue: 'Actif',
      validate: {
        isIn: {
          args: [['Actif', 'Inactif', 'Suspendu', 'Retraité']],
          msg: 'Statut invalide'
        }
      }
    },
    role: {
      type: DataTypes.ENUM('Utilisateur', 'Chef_Unite', 'RH', 'Administrateur'),
      defaultValue: 'Utilisateur',
      validate: {
        isIn: {
          args: [['Utilisateur', 'Chef_Unite', 'RH', 'Administrateur']],
          msg: 'Rôle invalide'
        }
      }
    },
    jours_conges_annuels: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
      validate: {
        isInt: {
          msg: 'Le nombre de jours de congés annuels doit être un entier'
        },
        min: {
          args: [0],
          msg: 'Le nombre de jours de congés annuels ne peut pas être négatif'
        },
        max: {
          args: [60],
          msg: 'Le nombre de jours de congés annuels ne peut pas dépasser 60'
        }
      }
    },
    jours_conges_restants: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
      validate: {
        isInt: {
          msg: 'Le nombre de jours de congés restants doit être un entier'
        },
        min: {
          args: [0],
          msg: 'Le nombre de jours de congés restants ne peut pas être négatif'
        }
      }
    },
    photo_profil: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isUrl: {
          msg: 'L\'URL de la photo de profil est invalide'
        }
      }
    },
    derniere_connexion: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'utilisateurs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    defaultScope: {
      attributes: { exclude: ['mot_de_passe'] }
    },
    scopes: {
      withPassword: {
        attributes: { include: ['mot_de_passe'] }
      }
    },
    indexes: [
      {
        unique: true,
        fields: ['matricule']
      },
      {
        unique: true,
        fields: ['email']
      },
      {
        fields: ['grade_id']
      },
      {
        fields: ['unite_id']
      },
      {
        fields: ['superieur_direct_id']
      },
      {
        fields: ['statut']
      },
      {
        fields: ['role']
      }
    ],
    hooks: {
      beforeValidate: (utilisateur) => {
        // Nettoyer et formater les données
        if (utilisateur.nom) {
          utilisateur.nom = utilisateur.nom.trim().toUpperCase();
        }
        if (utilisateur.prenom) {
          utilisateur.prenom = utilisateur.prenom.trim();
          utilisateur.prenom = utilisateur.prenom.charAt(0).toUpperCase() + 
                              utilisateur.prenom.slice(1).toLowerCase();
        }
        if (utilisateur.email) {
          utilisateur.email = utilisateur.email.trim().toLowerCase();
        }
        if (utilisateur.matricule) {
          utilisateur.matricule = utilisateur.matricule.trim().toUpperCase();
        }
        if (utilisateur.lieu_naissance) {
          utilisateur.lieu_naissance = utilisateur.lieu_naissance.trim();
        }
      },
      beforeCreate: async (utilisateur) => {
        // Hasher le mot de passe
        if (utilisateur.mot_de_passe) {
          utilisateur.mot_de_passe = await bcrypt.hash(utilisateur.mot_de_passe, 10);
        }
      },
      beforeUpdate: async (utilisateur) => {
        // Hasher le mot de passe s'il a été modifié
        if (utilisateur.changed('mot_de_passe') && utilisateur.mot_de_passe) {
          utilisateur.mot_de_passe = await bcrypt.hash(utilisateur.mot_de_passe, 10);
        }
      }
    }
  });

  // Méthodes d'instance
  Utilisateur.prototype.getNomComplet = function() {
    return `${this.prenom} ${this.nom}`;
  };

  Utilisateur.prototype.verifierMotDePasse = async function(motDePasse) {
    return await bcrypt.compare(motDePasse, this.mot_de_passe);
  };

  Utilisateur.prototype.estActif = function() {
    return this.statut === 'Actif';
  };

  Utilisateur.prototype.peutApprouver = function() {
    return ['Chef_Unite', 'RH', 'Administrateur'].includes(this.role);
  };

  Utilisateur.prototype.estAdministrateur = function() {
    return this.role === 'Administrateur';
  };

  Utilisateur.prototype.calculerAnciennete = function() {
    if (!this.date_incorporation) return 0;
    
    const maintenant = new Date();
    const incorporation = new Date(this.date_incorporation);
    const diffTime = Math.abs(maintenant - incorporation);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.floor(diffDays / 365.25); // Années d'ancienneté
  };

  Utilisateur.prototype.mettreAJourDerniereConnexion = async function() {
    this.derniere_connexion = new Date();
    await this.save();
  };

  // Méthodes de classe
  Utilisateur.authentifier = async function(email, motDePasse) {
    const utilisateur = await this.scope('withPassword').findOne({
      where: { email: email.toLowerCase() },
      include: ['grade', 'unite']
    });
    
    if (!utilisateur) {
      throw new Error('Email ou mot de passe incorrect');
    }
    
    if (!utilisateur.estActif()) {
      throw new Error('Compte désactivé');
    }
    
    const motDePasseValide = await utilisateur.verifierMotDePasse(motDePasse);
    if (!motDePasseValide) {
      throw new Error('Email ou mot de passe incorrect');
    }
    
    await utilisateur.mettreAJourDerniereConnexion();
    
    // Retourner sans le mot de passe
    return await this.findByPk(utilisateur.id, {
      include: ['grade', 'unite', 'superieur_direct']
    });
  };

  return Utilisateur;
};
