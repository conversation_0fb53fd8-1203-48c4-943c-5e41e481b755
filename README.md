# 🛡️ Système de Gestion des Congés Militaires

## 📋 Description

Le **Système de Gestion des Congés Militaires (SGC)** est une application web complète développée pour automatiser et optimiser la gestion des congés et permissions du personnel militaire. Le système offre une interface moderne et intuitive en français, avec un workflow d'approbation multi-niveaux et des fonctionnalités avancées de reporting.

## ✨ Fonctionnalités Principales

### 🎯 Gestion des Demandes
- ✅ Soumission de demandes de congés avec validation automatique
- ✅ Types de congés multiples (annuel, maladie, exceptionnel, maternité, etc.)
- ✅ Calcul automatique de la durée et vérification des conflits
- ✅ Upload de documents justificatifs
- ✅ Système de remplaçants

### 🔄 Workflow d'Approbation
- ✅ Approbation multi-niveaux (Supérieur direct → Chef d'unité → RH)
- ✅ Notifications automatiques à chaque étape
- ✅ Commentaires et motifs de refus
- ✅ Historique complet des actions

### 📊 Tableau de Bord
- ✅ Statistiques en temps réel
- ✅ Activités récentes
- ✅ Actions rapides
- ✅ Prochains jours fériés
- ✅ Solde de congés restants

### 📅 Planning et Calendrier
- ✅ Vue calendrier des congés de l'unité
- ✅ Planning mensuel, hebdomadaire et liste
- ✅ Visualisation des conflits potentiels
- ✅ Export des plannings

### 👥 Gestion des Utilisateurs
- ✅ Hiérarchie militaire (grades et unités)
- ✅ Rôles et permissions (Utilisateur, Chef d'unité, RH, Admin)
- ✅ Profils personnalisables
- ✅ Gestion des sessions sécurisées

### 🔔 Notifications
- ✅ Notifications en temps réel
- ✅ Rappels automatiques
- ✅ Alertes par email (configurable)
- ✅ Centre de notifications unifié

## 🏗️ Architecture Technique

### Frontend
- **HTML5** - Structure sémantique
- **CSS3** - Styles modernes avec variables CSS
- **JavaScript ES6+** - Logique côté client
- **Bootstrap 5** - Framework CSS responsive
- **Font Awesome** - Icônes

### Backend
- **Python 3** - Langage de programmation
- **Flask** - Framework web léger
- **Flask-CORS** - Gestion des requêtes cross-origin
- **Sessions** - Gestion des sessions utilisateur
- **JSON** - Format d'échange de données

### Base de Données
- **MySQL** - Base de données relationnelle
- **12 tables principales** avec relations complexes
- **Vues et procédures stockées** pour les rapports
- **Index optimisés** pour les performances

## 📁 Structure du Projet

```
PERMESSION/
├── 📁 database/
│   ├── schema.sql              # Structure de la base de données
│   ├── seed.sql               # Données initiales
│   └── procedures_views.sql   # Procédures et vues
├── 📁 models/
│   ├── index.js              # Configuration Sequelize
│   ├── Grade.js              # Modèle des grades
│   ├── Unite.js              # Modèle des unités
│   ├── Utilisateur.js        # Modèle des utilisateurs
│   ├── TypeConge.js          # Modèle des types de congés
│   ├── DemandeConge.js       # Modèle des demandes
│   ├── WorkflowApprobation.js # Modèle du workflow
│   ├── JourFerie.js          # Modèle des jours fériés
│   ├── HistoriqueConge.js    # Modèle de l'historique
│   ├── Notification.js       # Modèle des notifications
│   ├── SessionUtilisateur.js # Modèle des sessions
│   └── ParametreSysteme.js   # Modèle des paramètres
├── 📁 public/
│   ├── 📁 css/
│   │   ├── style.css         # Styles principaux
│   │   └── dashboard.css     # Styles du tableau de bord
│   ├── 📁 js/
│   │   ├── app.js           # Application principale
│   │   ├── dashboard.js     # Logique du tableau de bord
│   │   ├── demandes.js      # Gestion des demandes
│   │   └── utils.js         # Utilitaires
│   ├── 📁 pages/
│   │   └── nouvelle-demande.html # Page nouvelle demande
│   ├── 📁 images/
│   │   └── avatar-default.png    # Avatar par défaut
│   ├── index.html           # Page principale
│   └── login.html           # Page de connexion
└── README.md               # Documentation
```

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.7 ou supérieur
- Navigateur web moderne
- (Optionnel) MySQL pour la base de données complète

### Installation et Démarrage Rapide

#### 🚀 Méthode Simple (Recommandée)

1. **Télécharger le projet**
```bash
git clone <repository-url>
cd PERMESSION
```

2. **Démarrer le serveur**

**Sur Windows:**
```bash
run.bat
```

**Sur Linux/Mac:**
```bash
chmod +x run.sh
./run.sh
```

**Ou manuellement:**
```bash
# Installer les dépendances
pip install flask flask-cors

# Démarrer le serveur
python app.py
```

3. **Accéder à l'application**
- Ouvrir le navigateur sur: http://localhost:5000/login

#### 🔧 Installation Complète (Avec Base de Données)

1. **Installer Python et MySQL**
```bash
# Installer les dépendances Python
pip install -r requirements.txt
```

2. **Configurer la base de données MySQL**
```bash
# Créer la base de données
mysql -u root -p < database/schema.sql

# Insérer les données initiales
mysql -u root -p < database/seed.sql

# Ajouter les procédures et vues
mysql -u root -p < database/procedures_views.sql
```

3. **Démarrer l'application**
```bash
python app.py
```

## 🔐 Comptes de Démonstration

### Administrateur
- **Email**: `<EMAIL>`
- **Mot de passe**: `admin123`
- **Rôle**: Administrateur système

### Utilisateur Standard
- **Email**: `<EMAIL>`
- **Mot de passe**: `user123`
- **Rôle**: Capitaine - Utilisateur standard

## 📱 Interface Utilisateur

### Page de Connexion
- Design moderne avec gradient
- Authentification sécurisée
- Comptes de démonstration intégrés
- Responsive design

### Tableau de Bord
- Cartes de statistiques animées
- Timeline des activités récentes
- Actions rapides
- Prochains jours fériés

### Gestion des Demandes
- Formulaire intuitif avec validation
- Calcul automatique des durées
- Filtres et recherche avancée
- Actions en lot

### Planning Calendrier
- Vue mensuelle interactive
- Codes couleur par type de congé
- Navigation fluide
- Export des données

## 🔧 Configuration

### Paramètres Système
- Nombre de jours de congés annuels par défaut
- Délai minimum de soumission
- Durée maximale des congés continus
- Notifications email/SMS
- Fuseau horaire et langue

### Rôles et Permissions
- **Utilisateur**: Soumettre et consulter ses demandes
- **Chef d'Unité**: Approuver les demandes de son équipe
- **RH**: Gestion globale des congés et rapports
- **Administrateur**: Configuration système complète

## 📊 Base de Données

### Tables Principales
- `grades` - Hiérarchie militaire
- `unites` - Structure organisationnelle
- `utilisateurs` - Personnel militaire
- `types_conges` - Types de congés disponibles
- `demandes_conges` - Demandes soumises
- `workflow_approbations` - Processus d'approbation
- `jours_feries` - Calendrier des jours fériés
- `historique_conges` - Traçabilité des actions
- `notifications` - Système de notifications
- `sessions_utilisateurs` - Gestion des sessions
- `parametres_systeme` - Configuration globale

### Relations Clés
- Hiérarchie des unités militaires
- Relations supérieur/subordonné
- Workflow d'approbation multi-niveaux
- Historique complet des actions

## 🛡️ Sécurité

### Authentification
- Hachage des mots de passe avec bcrypt
- Tokens JWT pour les sessions
- Expiration automatique des sessions
- Protection contre les attaques CSRF

### Autorisation
- Contrôle d'accès basé sur les rôles
- Validation des permissions à chaque action
- Audit trail complet
- Chiffrement des données sensibles

## 📈 Performances

### Optimisations
- Index de base de données optimisés
- Mise en cache des requêtes fréquentes
- Compression des assets
- Lazy loading des composants

### Monitoring
- Logs détaillés des actions
- Métriques de performance
- Alertes automatiques
- Sauvegarde automatique

## 🌐 Internationalisation

### Langues Supportées
- **Français** (par défaut)
- **Arabe** (prévu)
- **Anglais** (prévu)

### Localisation
- Formats de date localisés
- Fuseau horaire configurable
- Calendrier hijri (prévu)
- Adaptation culturelle

## 🔮 Roadmap

### Version 2.0
- [ ] API REST complète
- [ ] Application mobile (React Native)
- [ ] Intégration avec Active Directory
- [ ] Rapports avancés avec graphiques
- [ ] Module de formation du personnel

### Version 3.0
- [ ] Intelligence artificielle pour la planification
- [ ] Intégration avec systèmes RH existants
- [ ] Module de gestion des missions
- [ ] Tableau de bord exécutif

## 🤝 Contribution

Les contributions sont les bienvenues ! Veuillez suivre ces étapes :

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou support technique :

- **Email**: <EMAIL>
- **Documentation**: [Wiki du projet](wiki-url)
- **Issues**: [GitHub Issues](issues-url)

## 🙏 Remerciements

- Ministère de la Défense Nationale d'Algérie
- Équipe de développement SGC
- Communauté open source

---

**Développé avec ❤️ pour le personnel militaire algérien**
