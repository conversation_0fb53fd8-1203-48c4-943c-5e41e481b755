#!/bin/bash

echo "==================================="
echo "   نظام إدارة الإجازات العسكرية"
echo "==================================="
echo ""
echo "تشغيل الخادم..."
echo ""

# تثبيت المتطلبات إذا لم تكن مثبتة
python3 -m pip install flask flask-cors --quiet

# تشغيل الخادم
echo "بدء تشغيل الخادم على المنفذ 5000..."
echo ""
echo "الروابط المتاحة:"
echo "صفحة الدخول: http://localhost:5000/login"
echo "لوحة التحكم: http://localhost:5000/"
echo ""
echo "حسابات التجربة:"
echo "مدير: <EMAIL> / admin123"
echo "مستخدم: <EMAIL> / user123"
echo ""
echo "اضغط Ctrl+C لإيقاف الخادم"
echo "==================================="
echo ""

python3 app.py
