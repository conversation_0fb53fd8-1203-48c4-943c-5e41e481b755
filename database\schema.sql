-- ===================================================================
-- SYSTÈME DE GESTION DES CONGÉS ET PERMISSIONS MILITAIRES
-- Base de données MySQL - Structure complète
-- ===================================================================

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS systeme_conges_militaire 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE systeme_conges_militaire;

-- ===================================================================
-- TABLE DES GRADES MILITAIRES
-- ===================================================================
CREATE TABLE grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom_grade VARCHAR(100) NOT NULL UNIQUE,
    niveau_hierarchique INT NOT NULL,
    type_personnel ENUM('Officier', 'Sous-Officier', 'Militaire du rang') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================================================
-- TABLE DES UNITÉS MILITAIRES
-- ===================================================================
CREATE TABLE unites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom_unite VARCHAR(200) NOT NULL,
    code_unite VARCHAR(20) UNIQUE NOT NULL,
    unite_parent_id INT NULL,
    commandant_id INT NULL,
    localisation VARCHAR(200),
    telephone VARCHAR(20),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (unite_parent_id) REFERENCES unites(id) ON DELETE SET NULL
);

-- ===================================================================
-- TABLE DES UTILISATEURS (PERSONNEL MILITAIRE)
-- ===================================================================
CREATE TABLE utilisateurs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    matricule VARCHAR(20) UNIQUE NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    grade_id INT NOT NULL,
    unite_id INT NOT NULL,
    superieur_direct_id INT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR(100),
    date_incorporation DATE,
    telephone VARCHAR(20),
    adresse TEXT,
    statut ENUM('Actif', 'Inactif', 'Suspendu', 'Retraité') DEFAULT 'Actif',
    role ENUM('Utilisateur', 'Chef_Unite', 'RH', 'Administrateur') DEFAULT 'Utilisateur',
    jours_conges_annuels INT DEFAULT 30,
    jours_conges_restants INT DEFAULT 30,
    photo_profil VARCHAR(255),
    derniere_connexion TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (grade_id) REFERENCES grades(id),
    FOREIGN KEY (unite_id) REFERENCES unites(id),
    FOREIGN KEY (superieur_direct_id) REFERENCES utilisateurs(id) ON DELETE SET NULL
);

-- ===================================================================
-- TABLE DES TYPES DE CONGÉS
-- ===================================================================
CREATE TABLE types_conges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom_type VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    duree_max_jours INT DEFAULT NULL,
    necessite_justificatif BOOLEAN DEFAULT FALSE,
    deductible_conges_annuels BOOLEAN DEFAULT TRUE,
    couleur_affichage VARCHAR(7) DEFAULT '#007bff',
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================================================
-- TABLE DES DEMANDES DE CONGÉS
-- ===================================================================
CREATE TABLE demandes_conges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    demandeur_id INT NOT NULL,
    type_conge_id INT NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    nombre_jours INT NOT NULL,
    motif TEXT,
    adresse_conge TEXT,
    telephone_conge VARCHAR(20),
    remplacant_id INT NULL,
    document_justificatif VARCHAR(255),
    statut ENUM('En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2', 'Approuve_final', 'Refuse', 'Annule') DEFAULT 'En_attente',
    commentaire_refus TEXT,
    date_soumission TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (demandeur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (type_conge_id) REFERENCES types_conges(id),
    FOREIGN KEY (remplacant_id) REFERENCES utilisateurs(id) ON DELETE SET NULL,
    FOREIGN KEY (traite_par_id) REFERENCES utilisateurs(id) ON DELETE SET NULL
);

-- ===================================================================
-- TABLE DU WORKFLOW D'APPROBATION
-- ===================================================================
CREATE TABLE workflow_approbations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    demande_id INT NOT NULL,
    niveau_approbation INT NOT NULL,
    approbateur_id INT NOT NULL,
    statut ENUM('En_attente', 'Approuve', 'Refuse') DEFAULT 'En_attente',
    commentaire TEXT,
    date_action TIMESTAMP NULL,
    ordre_traitement INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (demande_id) REFERENCES demandes_conges(id) ON DELETE CASCADE,
    FOREIGN KEY (approbateur_id) REFERENCES utilisateurs(id)
);

-- ===================================================================
-- TABLE DES JOURS FÉRIÉS
-- ===================================================================
CREATE TABLE jours_feries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom_ferie VARCHAR(200) NOT NULL,
    date_ferie DATE NOT NULL,
    type_ferie ENUM('National', 'Religieux', 'Militaire', 'Regional') DEFAULT 'National',
    recurrent BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================================================
-- TABLE DE L'HISTORIQUE DES CONGÉS
-- ===================================================================
CREATE TABLE historique_conges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    utilisateur_id INT NOT NULL,
    demande_id INT NOT NULL,
    action ENUM('Soumission', 'Approbation', 'Refus', 'Modification', 'Annulation') NOT NULL,
    details TEXT,
    effectue_par_id INT NOT NULL,
    date_action TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (demande_id) REFERENCES demandes_conges(id) ON DELETE CASCADE,
    FOREIGN KEY (effectue_par_id) REFERENCES utilisateurs(id)
);

-- ===================================================================
-- TABLE DES NOTIFICATIONS
-- ===================================================================
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    destinataire_id INT NOT NULL,
    titre VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type_notification ENUM('Info', 'Alerte', 'Rappel', 'Approbation') DEFAULT 'Info',
    demande_id INT NULL,
    lu BOOLEAN DEFAULT FALSE,
    date_lecture TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (destinataire_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (demande_id) REFERENCES demandes_conges(id) ON DELETE CASCADE
);

-- ===================================================================
-- TABLE DES SESSIONS UTILISATEURS
-- ===================================================================
CREATE TABLE sessions_utilisateurs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    utilisateur_id INT NOT NULL,
    token_session VARCHAR(255) NOT NULL UNIQUE,
    adresse_ip VARCHAR(45),
    user_agent TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_expiration TIMESTAMP NOT NULL,
    actif BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE
);

-- ===================================================================
-- TABLE DES PARAMÈTRES SYSTÈME
-- ===================================================================
CREATE TABLE parametres_systeme (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cle_parametre VARCHAR(100) NOT NULL UNIQUE,
    valeur_parametre TEXT NOT NULL,
    description_parametre TEXT,
    type_parametre ENUM('String', 'Integer', 'Boolean', 'JSON') DEFAULT 'String',
    modifiable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================================================
-- AJOUT DES CLÉS ÉTRANGÈRES MANQUANTES
-- ===================================================================
ALTER TABLE unites ADD FOREIGN KEY (commandant_id) REFERENCES utilisateurs(id) ON DELETE SET NULL;

-- ===================================================================
-- INDEX POUR OPTIMISER LES PERFORMANCES
-- ===================================================================
CREATE INDEX idx_utilisateurs_matricule ON utilisateurs(matricule);
CREATE INDEX idx_utilisateurs_email ON utilisateurs(email);
CREATE INDEX idx_demandes_statut ON demandes_conges(statut);
CREATE INDEX idx_demandes_dates ON demandes_conges(date_debut, date_fin);
CREATE INDEX idx_demandes_demandeur ON demandes_conges(demandeur_id);
CREATE INDEX idx_workflow_demande ON workflow_approbations(demande_id);
CREATE INDEX idx_notifications_destinataire ON notifications(destinataire_id);
CREATE INDEX idx_historique_utilisateur ON historique_conges(utilisateur_id);
CREATE INDEX idx_sessions_token ON sessions_utilisateurs(token_session);
