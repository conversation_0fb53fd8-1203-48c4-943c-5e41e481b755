// ===================================================================
// DASHBOARD - TABLEAU DE BORD
// ===================================================================

// Chargement du tableau de bord
async function loadDashboard() {
    try {
        // Charger les statistiques
        await loadDashboardStats();
        
        // Charger les activités récentes
        await loadRecentActivities();
        
        // Charger les jours fériés à venir
        await loadUpcomingHolidays();
        
        // Initialiser les graphiques si nécessaire
        initializeDashboardCharts();
        
    } catch (error) {
        console.error('Erreur lors du chargement du tableau de bord:', error);
        NotificationManager.error('Erreur lors du chargement du tableau de bord');
    }
}

// Chargement des statistiques
async function loadDashboardStats() {
    try {
        // Simuler des données pour le moment
        const stats = {
            congesRestants: 23,
            demandesApprouvees: 12,
            demandesEnAttente: 2,
            demandesRefusees: 1
        };
        
        // Mettre à jour les cartes de statistiques
        updateStatsCards(stats);
        
    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
    }
}

// Mise à jour des cartes de statistiques
function updateStatsCards(stats) {
    const cards = document.querySelectorAll('.card .h5');
    if (cards.length >= 4) {
        cards[0].textContent = `${stats.congesRestants} jours`;
        cards[1].textContent = stats.demandesApprouvees;
        cards[2].textContent = stats.demandesEnAttente;
        cards[3].textContent = stats.demandesRefusees;
    }
}

// Chargement des activités récentes
async function loadRecentActivities() {
    try {
        const activitiesContainer = document.getElementById('recent-activities');
        if (!activitiesContainer) return;
        
        // Simuler des activités récentes
        const activities = [
            {
                id: 1,
                type: 'success',
                icon: 'check-circle',
                title: 'Demande de congé approuvée',
                description: 'Votre demande de congé annuel du 15/12/2024 au 29/12/2024 a été approuvée',
                time: 'Il y a 2 heures'
            },
            {
                id: 2,
                type: 'info',
                icon: 'paper-plane',
                title: 'Nouvelle demande soumise',
                description: 'Demande de permission exceptionnelle soumise pour validation',
                time: 'Il y a 1 jour'
            },
            {
                id: 3,
                type: 'warning',
                icon: 'clock',
                title: 'Rappel de retour',
                description: 'Votre congé se termine dans 3 jours. N\'oubliez pas de reprendre le service',
                time: 'Il y a 2 jours'
            },
            {
                id: 4,
                type: 'danger',
                icon: 'times-circle',
                title: 'Demande refusée',
                description: 'Demande de congé exceptionnel refusée - Effectifs insuffisants',
                time: 'Il y a 3 jours'
            }
        ];
        
        activitiesContainer.innerHTML = activities.map(activity => `
            <div class="timeline-item">
                <div class="timeline-header">
                    <div class="timeline-title">
                        <i class="fas fa-${activity.icon} text-${activity.type} me-2"></i>
                        ${activity.title}
                    </div>
                    <div class="timeline-date">${activity.time}</div>
                </div>
                <div class="timeline-description">
                    ${activity.description}
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Erreur lors du chargement des activités:', error);
    }
}

// Chargement des jours fériés à venir
async function loadUpcomingHolidays() {
    try {
        const holidaysContainer = document.getElementById('upcoming-holidays');
        if (!holidaysContainer) return;
        
        // Simuler des jours fériés
        const holidays = [
            {
                id: 1,
                name: 'Nouvel An',
                date: '2024-01-01',
                type: 'National',
                daysRemaining: 15
            },
            {
                id: 2,
                name: 'Fête du Travail',
                date: '2024-05-01',
                type: 'National',
                daysRemaining: 135
            },
            {
                id: 3,
                name: 'Fête de l\'Indépendance',
                date: '2024-07-05',
                type: 'National',
                daysRemaining: 200
            }
        ];
        
        holidaysContainer.innerHTML = holidays.map(holiday => {
            const date = new Date(holiday.date);
            const day = date.getDate();
            const month = date.toLocaleDateString('fr-FR', { month: 'short' });
            
            return `
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0 me-3">
                        <div class="bg-primary text-white rounded text-center p-2" style="width: 50px;">
                            <div class="fw-bold">${day}</div>
                            <div style="font-size: 0.7rem;">${month.toUpperCase()}</div>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-semibold">${holiday.name}</div>
                        <div class="text-muted small">${holiday.type} - Dans ${holiday.daysRemaining} jours</div>
                    </div>
                </div>
            `;
        }).join('');
        
    } catch (error) {
        console.error('Erreur lors du chargement des jours fériés:', error);
    }
}

// Initialisation des graphiques
function initializeDashboardCharts() {
    // Graphique des congés par mois (exemple avec Chart.js si disponible)
    const chartContainer = document.getElementById('conges-chart');
    if (chartContainer && typeof Chart !== 'undefined') {
        const ctx = chartContainer.getContext('2d');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Congés pris',
                    data: [2, 4, 3, 5, 2, 3],
                    borderColor: 'rgb(44, 90, 160)',
                    backgroundColor: 'rgba(44, 90, 160, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Actualisation du tableau de bord
async function refreshDashboard() {
    LoadingManager.show();
    try {
        await loadDashboard();
        NotificationManager.success('Tableau de bord actualisé');
    } catch (error) {
        NotificationManager.error('Erreur lors de l\'actualisation');
    } finally {
        LoadingManager.hide();
    }
}

// Gestionnaire d'événements pour le tableau de bord
document.addEventListener('DOMContentLoaded', () => {
    // Bouton d'actualisation
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshDashboard);
    }
    
    // Auto-actualisation toutes les 5 minutes
    setInterval(async () => {
        if (AppState.currentPage === 'dashboard') {
            await loadDashboardStats();
        }
    }, 5 * 60 * 1000);
});

// Export des fonctions
window.loadDashboard = loadDashboard;
window.refreshDashboard = refreshDashboard;
