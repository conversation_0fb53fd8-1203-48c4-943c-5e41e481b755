// ===================================================================
// MODÈLE NOTIFICATION - NOTIFICATIONS ET ALERTES SYSTÈME
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    destinataire_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    titre: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le titre de la notification ne peut pas être vide'
        },
        len: {
          args: [3, 200],
          msg: 'Le titre doit contenir entre 3 et 200 caractères'
        }
      }
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le message de la notification ne peut pas être vide'
        },
        len: {
          args: [10, 2000],
          msg: 'Le message doit contenir entre 10 et 2000 caractères'
        }
      }
    },
    type_notification: {
      type: DataTypes.ENUM('Info', 'Alerte', 'Rappel', 'Approbation'),
      defaultValue: 'Info',
      validate: {
        isIn: {
          args: [['Info', 'Alerte', 'Rappel', 'Approbation']],
          msg: 'Type de notification invalide'
        }
      }
    },
    demande_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'demandes_conges',
        key: 'id'
      }
    },
    lu: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      validate: {
        isBoolean: {
          msg: 'Le statut de lecture doit être vrai ou faux'
        }
      }
    },
    date_lecture: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'notifications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['destinataire_id']
      },
      {
        fields: ['demande_id']
      },
      {
        fields: ['type_notification']
      },
      {
        fields: ['lu']
      },
      {
        fields: ['created_at']
      }
    ],
    hooks: {
      beforeValidate: (notification) => {
        // Nettoyer et formater les données
        if (notification.titre) {
          notification.titre = notification.titre.trim();
        }
        if (notification.message) {
          notification.message = notification.message.trim();
        }
      },
      beforeUpdate: (notification) => {
        // Mettre à jour la date de lecture si marquée comme lue
        if (notification.changed('lu') && notification.lu === true && !notification.date_lecture) {
          notification.date_lecture = new Date();
        }
      }
    }
  });

  // Méthodes d'instance
  Notification.prototype.estLue = function() {
    return this.lu === true;
  };

  Notification.prototype.estNonLue = function() {
    return this.lu === false;
  };

  Notification.prototype.estInfo = function() {
    return this.type_notification === 'Info';
  };

  Notification.prototype.estAlerte = function() {
    return this.type_notification === 'Alerte';
  };

  Notification.prototype.estRappel = function() {
    return this.type_notification === 'Rappel';
  };

  Notification.prototype.estApprobation = function() {
    return this.type_notification === 'Approbation';
  };

  Notification.prototype.marquerCommeLue = async function() {
    this.lu = true;
    this.date_lecture = new Date();
    await this.save();
  };

  Notification.prototype.marquerCommeNonLue = async function() {
    this.lu = false;
    this.date_lecture = null;
    await this.save();
  };

  Notification.prototype.getTypeLibelle = function() {
    const types = {
      'Info': 'Information',
      'Alerte': 'Alerte',
      'Rappel': 'Rappel',
      'Approbation': 'Approbation'
    };
    
    return types[this.type_notification] || this.type_notification;
  };

  Notification.prototype.getTypeCouleur = function() {
    const couleurs = {
      'Info': '#17a2b8',
      'Alerte': '#dc3545',
      'Rappel': '#ffc107',
      'Approbation': '#28a745'
    };
    
    return couleurs[this.type_notification] || '#6c757d';
  };

  Notification.prototype.getTypeIcone = function() {
    const icones = {
      'Info': 'fas fa-info-circle',
      'Alerte': 'fas fa-exclamation-triangle',
      'Rappel': 'fas fa-bell',
      'Approbation': 'fas fa-check-circle'
    };
    
    return icones[this.type_notification] || 'fas fa-envelope';
  };

  Notification.prototype.getTempsEcoule = function() {
    const maintenant = new Date();
    const diffTime = maintenant - this.created_at;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffMinutes > 0) {
      return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      return 'À l\'instant';
    }
  };

  Notification.prototype.estRecente = function() {
    const maintenant = new Date();
    const diffTime = maintenant - this.created_at;
    const diffHours = diffTime / (1000 * 60 * 60);
    
    return diffHours <= 24; // Récente si moins de 24h
  };

  // Méthodes de classe
  Notification.creerNotification = async function(destinataireId, titre, message, type = 'Info', demandeId = null) {
    return await this.create({
      destinataire_id: destinataireId,
      titre: titre,
      message: message,
      type_notification: type,
      demande_id: demandeId
    });
  };

  Notification.getNotificationsParUtilisateur = async function(utilisateurId, limite = 50) {
    return await this.findAll({
      where: { destinataire_id: utilisateurId },
      include: [
        {
          association: 'demande',
          include: ['type_conge', 'demandeur']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: limite
    });
  };

  Notification.getNotificationsNonLues = async function(utilisateurId) {
    return await this.findAll({
      where: {
        destinataire_id: utilisateurId,
        lu: false
      },
      include: [
        {
          association: 'demande',
          include: ['type_conge', 'demandeur']
        }
      ],
      order: [['created_at', 'DESC']]
    });
  };

  Notification.compterNotificationsNonLues = async function(utilisateurId) {
    return await this.count({
      where: {
        destinataire_id: utilisateurId,
        lu: false
      }
    });
  };

  Notification.getNotificationsParType = async function(utilisateurId, type) {
    return await this.findAll({
      where: {
        destinataire_id: utilisateurId,
        type_notification: type
      },
      include: [
        {
          association: 'demande',
          include: ['type_conge', 'demandeur']
        }
      ],
      order: [['created_at', 'DESC']]
    });
  };

  Notification.marquerToutesCommeLues = async function(utilisateurId) {
    return await this.update(
      {
        lu: true,
        date_lecture: new Date()
      },
      {
        where: {
          destinataire_id: utilisateurId,
          lu: false
        }
      }
    );
  };

  Notification.supprimerNotificationsLues = async function(utilisateurId) {
    return await this.destroy({
      where: {
        destinataire_id: utilisateurId,
        lu: true
      }
    });
  };

  Notification.getNotificationsRecentes = async function(utilisateurId, heures = 24) {
    const { Op } = sequelize.Sequelize;
    const dateLimit = new Date();
    dateLimit.setHours(dateLimit.getHours() - heures);
    
    return await this.findAll({
      where: {
        destinataire_id: utilisateurId,
        created_at: {
          [Op.gte]: dateLimit
        }
      },
      include: [
        {
          association: 'demande',
          include: ['type_conge', 'demandeur']
        }
      ],
      order: [['created_at', 'DESC']]
    });
  };

  Notification.getStatistiquesNotifications = async function(utilisateurId) {
    const [total, nonLues, alertes, rappels] = await Promise.all([
      this.count({ where: { destinataire_id: utilisateurId } }),
      this.count({ where: { destinataire_id: utilisateurId, lu: false } }),
      this.count({ where: { destinataire_id: utilisateurId, type_notification: 'Alerte' } }),
      this.count({ where: { destinataire_id: utilisateurId, type_notification: 'Rappel' } })
    ]);
    
    return {
      total,
      nonLues,
      alertes,
      rappels,
      tauxLecture: total > 0 ? Math.round(((total - nonLues) / total) * 100) : 0
    };
  };

  Notification.nettoyerAnciennesNotifications = async function(joursAnciennete = 30) {
    const { Op } = sequelize.Sequelize;
    const dateLimit = new Date();
    dateLimit.setDate(dateLimit.getDate() - joursAnciennete);
    
    return await this.destroy({
      where: {
        created_at: {
          [Op.lt]: dateLimit
        },
        lu: true
      }
    });
  };

  // Scopes
  Notification.addScope('nonLues', {
    where: { lu: false }
  });

  Notification.addScope('lues', {
    where: { lu: true }
  });

  Notification.addScope('alertes', {
    where: { type_notification: 'Alerte' }
  });

  Notification.addScope('rappels', {
    where: { type_notification: 'Rappel' }
  });

  Notification.addScope('infos', {
    where: { type_notification: 'Info' }
  });

  Notification.addScope('approbations', {
    where: { type_notification: 'Approbation' }
  });

  Notification.addScope('recentes', {
    order: [['created_at', 'DESC']],
    limit: 20
  });

  Notification.addScope('avecDemande', {
    include: [
      {
        association: 'demande',
        include: ['type_conge', 'demandeur']
      }
    ]
  });

  return Notification;
};
