// ===================================================================
// SIDEBAR - إدارة الشريط الجانبي
// ===================================================================

document.addEventListener('DOMContentLoaded', function() {
    initializeSidebarFeatures();
});

// تهيئة ميزات الشريط الجانبي المتقدمة
function initializeSidebarFeatures() {
    // إضافة تأثيرات التمرير
    addHoverEffects();
    
    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();
    
    // إضافة تأثيرات الانتقال
    addTransitionEffects();
    
    // تحديث الإحصائيات
    updateSidebarStats();
    
    // إضافة tooltip للعناصر المطوية
    addTooltips();
}

// إضافة تأثيرات التمرير
function addHoverEffects() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            // إضافة تأثير الضوء
            this.style.boxShadow = 'inset 0 0 20px rgba(255, 255, 255, 0.1)';
            
            // تحريك الأيقونة
            const icon = this.querySelector('.nav-icon');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(5deg)';
            }
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
            
            const icon = this.querySelector('.nav-icon');
            if (icon) {
                icon.style.transform = '';
            }
        });
    });
}

// إضافة اختصارات لوحة المفاتيح
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + B لتبديل الشريط الجانبي
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
        
        // Escape لإغلاق الشريط الجانبي في الموبايل
        if (e.key === 'Escape') {
            closeSidebar();
        }
        
        // اختصارات للصفحات
        if (e.ctrlKey && e.shiftKey) {
            switch(e.key) {
                case 'D':
                    e.preventDefault();
                    showPage('dashboard');
                    break;
                case 'M':
                    e.preventDefault();
                    showPage('demandes');
                    break;
                case 'N':
                    e.preventDefault();
                    showPage('nouvelle-demande');
                    break;
                case 'A':
                    e.preventDefault();
                    showPage('approbations');
                    break;
                case 'P':
                    e.preventDefault();
                    showPage('planning');
                    break;
            }
        }
    });
}

// إضافة تأثيرات الانتقال
function addTransitionEffects() {
    const sidebar = document.getElementById('sidebar');
    
    // تأثير الظهور التدريجي للعناصر
    const navItems = sidebar.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('fade-in-left');
    });
    
    // تأثير النبض للإشعارات
    const badges = sidebar.querySelectorAll('.nav-badge');
    badges.forEach(badge => {
        if (parseInt(badge.textContent) > 0) {
            badge.classList.add('pulse');
        }
    });
}

// تحديث إحصائيات الشريط الجانبي
function updateSidebarStats() {
    // تحديث عدد الأيام المتبقية
    const remainingDays = document.querySelector('.sidebar-footer .stat-value');
    if (remainingDays && AppState.currentUser) {
        remainingDays.textContent = AppState.currentUser.jours_conges_restants || '23';
        
        // تغيير اللون حسب الكمية
        const days = parseInt(remainingDays.textContent);
        if (days < 5) {
            remainingDays.style.color = '#dc3545'; // أحمر
        } else if (days < 15) {
            remainingDays.style.color = '#ffc107'; // أصفر
        } else {
            remainingDays.style.color = '#ffd700'; // ذهبي
        }
    }
    
    // تحديث عدد الطلبات المعلقة
    const pendingRequests = document.querySelectorAll('.sidebar-footer .stat-value')[1];
    if (pendingRequests) {
        const pendingCount = AppState.demandes ? 
            AppState.demandes.filter(d => d.statut === 'En_attente').length : 2;
        pendingRequests.textContent = pendingCount;
    }
}

// إضافة tooltips للعناصر المطوية
function addTooltips() {
    const sidebar = document.getElementById('sidebar');
    const navLinks = sidebar.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const text = link.querySelector('.nav-text');
        if (text) {
            link.setAttribute('title', text.textContent);
            link.setAttribute('data-bs-placement', 'right');
        }
    });
    
    // تفعيل tooltips عند طي الشريط الجانبي
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const isCollapsed = sidebar.classList.contains('collapsed');
                
                navLinks.forEach(link => {
                    if (isCollapsed) {
                        // تفعيل tooltip
                        if (typeof bootstrap !== 'undefined') {
                            new bootstrap.Tooltip(link);
                        }
                    } else {
                        // إلغاء tooltip
                        const tooltip = bootstrap.Tooltip.getInstance(link);
                        if (tooltip) {
                            tooltip.dispose();
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(sidebar, { attributes: true });
}

// تحديث حالة الاتصال
function updateConnectionStatus(isOnline) {
    const statusIndicator = document.querySelector('.status-indicator');
    if (statusIndicator) {
        statusIndicator.className = 'status-indicator ' + (isOnline ? 'online' : 'offline');
    }
}

// مراقبة حالة الاتصال
window.addEventListener('online', () => updateConnectionStatus(true));
window.addEventListener('offline', () => updateConnectionStatus(false));

// تحديث الوقت في الشريط الجانبي
function updateSidebarTime() {
    const timeElement = document.getElementById('sidebar-time');
    if (timeElement) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        timeElement.textContent = timeString;
    }
}

// تحديث الوقت كل دقيقة
setInterval(updateSidebarTime, 60000);
updateSidebarTime();

// إضافة تأثيرات CSS ديناميكية
const style = document.createElement('style');
style.textContent = `
    .fade-in-left {
        animation: fadeInLeft 0.6s ease-out forwards;
        opacity: 0;
        transform: translateX(-20px);
    }
    
    @keyframes fadeInLeft {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .nav-link {
        position: relative;
        overflow: hidden;
    }
    
    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }
    
    .nav-link:hover::before {
        left: 100%;
    }
    
    .sidebar-user:hover .user-avatar img {
        transform: scale(1.1) rotate(5deg);
    }
    
    .sidebar-footer .stat-item:hover .stat-value {
        transform: scale(1.2);
        text-shadow: 0 0 10px currentColor;
    }
`;

document.head.appendChild(style);

// تصدير الوظائف
window.updateSidebarStats = updateSidebarStats;
window.updateConnectionStatus = updateConnectionStatus;
