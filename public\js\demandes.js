// ===================================================================
// GESTION DES DEMANDES DE CONGÉS
// ===================================================================

// Chargement de la page des demandes
async function loadDemandes() {
    try {
        const demandesPage = document.getElementById('demandes-page');
        if (!demandesPage) return;
        
        demandesPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-1">
                                    <i class="fas fa-calendar-plus text-primary me-2"></i>
                                    <PERSON><PERSON><PERSON> de Congés
                                </h1>
                                <p class="text-muted"><PERSON><PERSON><PERSON> vos demandes de congés et permissions</p>
                            </div>
                            <button class="btn btn-primary" onclick="showPage('nouvelle-demande')">
                                <i class="fas fa-plus me-2"></i>Nouvelle Demande
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Filtres -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">Statut</label>
                                        <select class="form-select" id="filter-statut">
                                            <option value="">Tous les statuts</option>
                                            <option value="En_attente">En attente</option>
                                            <option value="Approuve_final">Approuvé</option>
                                            <option value="Refuse">Refusé</option>
                                            <option value="Annule">Annulé</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Type de congé</label>
                                        <select class="form-select" id="filter-type">
                                            <option value="">Tous les types</option>
                                            <option value="1">Congé Annuel</option>
                                            <option value="2">Congé de Maladie</option>
                                            <option value="3">Congé Exceptionnel</option>
                                            <option value="4">Permission de Sortie</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Année</label>
                                        <select class="form-select" id="filter-annee">
                                            <option value="">Toutes les années</option>
                                            <option value="2024" selected>2024</option>
                                            <option value="2023">2023</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button class="btn btn-outline-primary" onclick="applyFilters()">
                                                <i class="fas fa-filter me-2"></i>Filtrer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Liste des demandes -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-list me-2"></i>Liste des Demandes
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="demandes-list">
                                    <!-- Les demandes seront chargées ici -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Charger les demandes
        await loadDemandesList();
        
        // Initialiser les événements
        initializeDemandesEvents();
        
    } catch (error) {
        console.error('Erreur lors du chargement des demandes:', error);
        NotificationManager.error('Erreur lors du chargement des demandes');
    }
}

// Chargement de la liste des demandes
async function loadDemandesList() {
    try {
        const demandesContainer = document.getElementById('demandes-list');
        if (!demandesContainer) return;
        
        // Simuler des demandes
        const demandes = [
            {
                id: 1,
                type: 'Congé Annuel',
                dateDebut: '2024-12-15',
                dateFin: '2024-12-29',
                nombreJours: 15,
                statut: 'Approuve_final',
                dateSubmission: '2024-11-20',
                motif: 'Congé de fin d\'année'
            },
            {
                id: 2,
                type: 'Permission Exceptionnelle',
                dateDebut: '2024-12-10',
                dateFin: '2024-12-10',
                nombreJours: 1,
                statut: 'En_attente',
                dateSubmission: '2024-12-05',
                motif: 'Rendez-vous médical'
            },
            {
                id: 3,
                type: 'Congé de Maladie',
                dateDebut: '2024-11-15',
                dateFin: '2024-11-17',
                nombreJours: 3,
                statut: 'Refuse',
                dateSubmission: '2024-11-14',
                motif: 'Certificat médical non conforme',
                commentaireRefus: 'Le certificat médical fourni ne respecte pas les normes requises'
            }
        ];
        
        if (demandes.length === 0) {
            demandesContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune demande trouvée</h5>
                    <p class="text-muted">Vous n'avez pas encore soumis de demande de congé.</p>
                    <button class="btn btn-primary" onclick="showPage('nouvelle-demande')">
                        <i class="fas fa-plus me-2"></i>Créer une demande
                    </button>
                </div>
            `;
            return;
        }
        
        demandesContainer.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Période</th>
                            <th>Durée</th>
                            <th>Statut</th>
                            <th>Date de soumission</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${demandes.map(demande => `
                            <tr>
                                <td>
                                    <div class="fw-semibold">${demande.type}</div>
                                    <div class="text-muted small">${demande.motif}</div>
                                </td>
                                <td>
                                    <div>${Utils.formatDate(demande.dateDebut)}</div>
                                    <div class="text-muted small">au ${Utils.formatDate(demande.dateFin)}</div>
                                </td>
                                <td>
                                    <span class="badge bg-info">${demande.nombreJours} jour${demande.nombreJours > 1 ? 's' : ''}</span>
                                </td>
                                <td>
                                    ${getStatutBadge(demande.statut)}
                                </td>
                                <td>${Utils.formatDate(demande.dateSubmission)}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewDemande(${demande.id})" title="Voir détails">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        ${demande.statut === 'En_attente' ? `
                                            <button class="btn btn-outline-warning" onclick="editDemande(${demande.id})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="cancelDemande(${demande.id})" title="Annuler">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
    } catch (error) {
        console.error('Erreur lors du chargement de la liste:', error);
    }
}

// Génération du badge de statut
function getStatutBadge(statut) {
    const badges = {
        'En_attente': '<span class="badge bg-warning">En attente</span>',
        'Approuve_final': '<span class="badge bg-success">Approuvé</span>',
        'Refuse': '<span class="badge bg-danger">Refusé</span>',
        'Annule': '<span class="badge bg-secondary">Annulé</span>'
    };
    return badges[statut] || '<span class="badge bg-secondary">Inconnu</span>';
}

// Application des filtres
function applyFilters() {
    const statut = document.getElementById('filter-statut')?.value;
    const type = document.getElementById('filter-type')?.value;
    const annee = document.getElementById('filter-annee')?.value;
    
    // Ici, vous appelleriez l'API avec les filtres
    console.log('Filtres appliqués:', { statut, type, annee });
    
    // Recharger la liste avec les filtres
    loadDemandesList();
    
    NotificationManager.info('Filtres appliqués');
}

// Voir les détails d'une demande
function viewDemande(id) {
    // Créer et afficher un modal avec les détails
    const modalHtml = `
        <div class="modal fade" id="demandeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-calendar-alt me-2"></i>Détails de la demande
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="demande-details">
                            <!-- Détails chargés ici -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('demandeModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Ajouter le nouveau modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Charger les détails
    loadDemandeDetails(id);
    
    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('demandeModal'));
    modal.show();
}

// Chargement des détails d'une demande
async function loadDemandeDetails(id) {
    try {
        const detailsContainer = document.getElementById('demande-details');
        if (!detailsContainer) return;
        
        // Simuler les détails d'une demande
        const demande = {
            id: id,
            type: 'Congé Annuel',
            dateDebut: '2024-12-15',
            dateFin: '2024-12-29',
            nombreJours: 15,
            statut: 'Approuve_final',
            motif: 'Congé de fin d\'année pour passer du temps en famille',
            adresseConge: '123 Rue de la Paix, Alger',
            telephoneConge: '+213 555 123 456',
            dateSubmission: '2024-11-20',
            remplacant: 'Lieutenant BENALI Ahmed'
        };
        
        detailsContainer.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary mb-3">Informations générales</h6>
                    <table class="table table-sm">
                        <tr>
                            <td class="fw-semibold">Type de congé:</td>
                            <td>${demande.type}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Date de début:</td>
                            <td>${Utils.formatDate(demande.dateDebut)}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Date de fin:</td>
                            <td>${Utils.formatDate(demande.dateFin)}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Nombre de jours:</td>
                            <td><span class="badge bg-info">${demande.nombreJours} jours</span></td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Statut:</td>
                            <td>${getStatutBadge(demande.statut)}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary mb-3">Détails du congé</h6>
                    <table class="table table-sm">
                        <tr>
                            <td class="fw-semibold">Adresse pendant le congé:</td>
                            <td>${demande.adresseConge}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Téléphone:</td>
                            <td>${demande.telephoneConge}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Remplaçant:</td>
                            <td>${demande.remplacant}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Date de soumission:</td>
                            <td>${Utils.formatDate(demande.dateSubmission)}</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-primary mb-2">Motif</h6>
                    <p class="bg-light p-3 rounded">${demande.motif}</p>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Erreur lors du chargement des détails:', error);
    }
}

// Modifier une demande
function editDemande(id) {
    // Rediriger vers la page de modification
    showPage('nouvelle-demande');
    // Ici, vous chargeriez les données de la demande pour modification
    console.log('Modification de la demande:', id);
}

// Annuler une demande
async function cancelDemande(id) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette demande ?')) {
        try {
            // Appel API pour annuler
            // await ApiManager.post(`/demandes/${id}/cancel`);
            
            NotificationManager.success('Demande annulée avec succès');
            await loadDemandesList();
        } catch (error) {
            NotificationManager.error('Erreur lors de l\'annulation');
        }
    }
}

// Initialisation des événements
function initializeDemandesEvents() {
    // Événements pour les filtres
    const filterElements = ['filter-statut', 'filter-type', 'filter-annee'];
    filterElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', applyFilters);
        }
    });
}

// Export des fonctions
window.loadDemandes = loadDemandes;
window.viewDemande = viewDemande;
window.editDemande = editDemande;
window.cancelDemande = cancelDemande;
window.applyFilters = applyFilters;
