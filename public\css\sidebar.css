/* ===================================================================
   SIDEBAR - شريط التنقل الجانبي العصري
   =================================================================== */

:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
    --sidebar-text: #ffffff;
    --sidebar-text-muted: rgba(255, 255, 255, 0.7);
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    --sidebar-active: rgba(255, 255, 255, 0.2);
    --sidebar-border: rgba(255, 255, 255, 0.1);
    --sidebar-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* ===================================================================
   LAYOUT ADJUSTMENTS
   =================================================================== */

body {
    padding-left: var(--sidebar-width);
    transition: padding-left var(--transition-speed) ease;
}

body.sidebar-collapsed {
    padding-left: var(--sidebar-collapsed-width);
}

body.sidebar-hidden {
    padding-left: 0;
}

.main-content {
    transition: margin-left var(--transition-speed) ease;
    min-height: calc(100vh - var(--navbar-height));
}

/* ===================================================================
   SIDEBAR CONTAINER
   =================================================================== */

.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    z-index: 1025;
    display: flex;
    flex-direction: column;
    box-shadow: var(--sidebar-shadow);
    transition: all var(--transition-speed) ease;
    overflow: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.hidden {
    transform: translateX(-100%);
}

/* ===================================================================
   SIDEBAR HEADER
   =================================================================== */

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--sidebar-border);
    background: rgba(0, 0, 0, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.sidebar-brand i {
    font-size: 2rem;
    margin-right: 15px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.brand-text {
    transition: opacity var(--transition-speed) ease;
}

.sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* ===================================================================
   SIDEBAR CONTENT
   =================================================================== */

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px 0;
}

.sidebar-content::-webkit-scrollbar {
    width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: var(--sidebar-border);
    border-radius: 2px;
}

/* ===================================================================
   USER INFO SECTION
   =================================================================== */

.sidebar-user {
    display: flex;
    align-items: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid var(--sidebar-border);
    margin-bottom: 20px;
    transition: all var(--transition-speed) ease;
}

.user-avatar {
    position: relative;
    margin-right: 15px;
    flex-shrink: 0;
}

.user-avatar img {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-speed) ease;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--sidebar-bg);
}

.status-indicator.online {
    background: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
}

.status-indicator.away {
    background: #ffc107;
}

.status-indicator.offline {
    background: #6c757d;
}

.user-info {
    flex: 1;
    min-width: 0;
    transition: opacity var(--transition-speed) ease;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.8rem;
    color: var(--sidebar-text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.user-time {
    font-size: 0.7rem;
    color: #ffd700;
    font-weight: 600;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
}

.sidebar.collapsed .user-info {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .user-avatar {
    margin-right: 0;
}

/* ===================================================================
   NAVIGATION MENU
   =================================================================== */

.sidebar-nav {
    flex: 1;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
    position: relative;
    border-radius: 0;
}

.nav-link:hover {
    background: var(--sidebar-hover);
    color: var(--sidebar-text);
    transform: translateX(5px);
}

.nav-link.active {
    background: var(--sidebar-active);
    border-right: 4px solid #ffd700;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 15px;
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: all var(--transition-speed) ease;
}

.nav-text {
    flex: 1;
    font-weight: 500;
    transition: opacity var(--transition-speed) ease;
    white-space: nowrap;
    overflow: hidden;
}

.nav-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    transition: opacity var(--transition-speed) ease;
}

.nav-badge.bg-warning {
    background: #ffc107;
    color: #000;
}

.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-badge {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
}

/* ===================================================================
   NAVIGATION DIVIDERS
   =================================================================== */

.nav-divider {
    margin: 20px 0 10px;
    padding: 0 20px;
}

.divider-text {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--sidebar-text-muted);
    transition: opacity var(--transition-speed) ease;
}

.sidebar.collapsed .divider-text {
    opacity: 0;
}

.sidebar.collapsed .nav-divider {
    margin: 10px 0 5px;
    padding: 0;
}

.sidebar.collapsed .nav-divider::after {
    content: '';
    display: block;
    width: 30px;
    height: 1px;
    background: var(--sidebar-border);
    margin: 0 auto;
}

/* ===================================================================
   SIDEBAR FOOTER
   =================================================================== */

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--sidebar-border);
    background: rgba(0, 0, 0, 0.1);
}

.footer-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    transition: opacity var(--transition-speed) ease;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffd700;
    line-height: 1;
}

.stat-label {
    font-size: 0.7rem;
    color: var(--sidebar-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

.sidebar.collapsed .footer-stats {
    opacity: 0;
    height: 0;
    margin: 0;
    overflow: hidden;
}

/* ===================================================================
   SIDEBAR OVERLAY (MOBILE)
   =================================================================== */

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1024;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-speed) ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===================================================================
   NOTIFICATION DROPDOWN
   =================================================================== */

.notification-dropdown {
    min-width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown .dropdown-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
}

.notification-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

.notification-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */

@media (max-width: 768px) {
    body {
        padding-left: 0;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 100%;
    }
    
    .sidebar-user {
        padding: 15px;
    }
    
    .user-avatar img {
        width: 40px;
        height: 40px;
    }
    
    .nav-link {
        padding: 10px 15px;
    }
    
    .sidebar-footer {
        padding: 15px;
    }
}

/* ===================================================================
   ANIMATIONS
   =================================================================== */

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.sidebar {
    animation: slideInLeft 0.5s ease-out;
}

.nav-link {
    animation: fadeIn 0.3s ease-out;
}

/* ===================================================================
   HOVER EFFECTS
   =================================================================== */

.nav-link:hover .nav-icon {
    transform: scale(1.1);
    color: #ffd700;
}

.nav-link.active .nav-icon {
    color: #ffd700;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
}

.user-avatar:hover img {
    transform: scale(1.05);
    border-color: #ffd700;
}

/* ===================================================================
   DARK MODE SUPPORT
   =================================================================== */

@media (prefers-color-scheme: dark) {
    .sidebar {
        --sidebar-bg: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
    }
}
