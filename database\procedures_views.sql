-- ===================================================================
-- PROCÉDURES STOCKÉES ET VUES POUR LE SYSTÈME DE GESTION DES CONGÉS
-- ===================================================================

USE systeme_conges_militaire;

-- ===================================================================
-- VUES UTILES POUR LE SYSTÈME
-- ===================================================================

-- Vue pour afficher les informations complètes des utilisateurs
CREATE OR REPLACE VIEW vue_utilisateurs_complet AS
SELECT 
    u.id,
    u.matricule,
    CONCAT(u.nom, ' ', u.prenom) AS nom_complet,
    u.email,
    g.nom_grade,
    g.type_personnel,
    un.nom_unite,
    un.code_unite,
    sup.nom AS nom_superieur,
    sup.prenom AS prenom_superieur,
    u.jours_conges_annuels,
    u.jours_conges_restants,
    u.statut,
    u.role,
    u.derniere_connexion,
    u.created_at
FROM utilisateurs u
LEFT JOIN grades g ON u.grade_id = g.id
LEFT JOIN unites un ON u.unite_id = un.id
LEFT JOIN utilisateurs sup ON u.superieur_direct_id = sup.id;

-- Vue pour les demandes de congés avec détails
CREATE OR REPLACE VIEW vue_demandes_conges_detaillees AS
SELECT 
    dc.id,
    dc.demandeur_id,
    CONCAT(u.nom, ' ', u.prenom) AS nom_demandeur,
    u.matricule,
    g.nom_grade,
    un.nom_unite,
    tc.nom_type AS type_conge,
    tc.couleur_affichage,
    dc.date_debut,
    dc.date_fin,
    dc.nombre_jours,
    dc.motif,
    dc.statut,
    dc.date_soumission,
    dc.date_traitement,
    CONCAT(t.nom, ' ', t.prenom) AS traite_par,
    dc.commentaire_refus,
    CASE 
        WHEN dc.remplacant_id IS NOT NULL THEN CONCAT(r.nom, ' ', r.prenom)
        ELSE NULL
    END AS nom_remplacant
FROM demandes_conges dc
JOIN utilisateurs u ON dc.demandeur_id = u.id
JOIN grades g ON u.grade_id = g.id
JOIN unites un ON u.unite_id = un.id
JOIN types_conges tc ON dc.type_conge_id = tc.id
LEFT JOIN utilisateurs t ON dc.traite_par_id = t.id
LEFT JOIN utilisateurs r ON dc.remplacant_id = r.id;

-- Vue pour les statistiques des congés par unité
CREATE OR REPLACE VIEW vue_stats_conges_unite AS
SELECT 
    un.id AS unite_id,
    un.nom_unite,
    un.code_unite,
    COUNT(DISTINCT u.id) AS nombre_personnel,
    COUNT(dc.id) AS total_demandes,
    COUNT(CASE WHEN dc.statut = 'Approuve_final' THEN 1 END) AS demandes_approuvees,
    COUNT(CASE WHEN dc.statut = 'Refuse' THEN 1 END) AS demandes_refusees,
    COUNT(CASE WHEN dc.statut IN ('En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2') THEN 1 END) AS demandes_en_cours,
    AVG(dc.nombre_jours) AS moyenne_jours_demandes,
    SUM(CASE WHEN dc.statut = 'Approuve_final' THEN dc.nombre_jours ELSE 0 END) AS total_jours_approuves
FROM unites un
LEFT JOIN utilisateurs u ON un.id = u.unite_id
LEFT JOIN demandes_conges dc ON u.id = dc.demandeur_id
WHERE u.statut = 'Actif'
GROUP BY un.id, un.nom_unite, un.code_unite;

-- Vue pour le planning des congés
CREATE OR REPLACE VIEW vue_planning_conges AS
SELECT 
    dc.id,
    dc.demandeur_id,
    CONCAT(u.nom, ' ', u.prenom) AS nom_demandeur,
    u.matricule,
    g.nom_grade,
    un.nom_unite,
    tc.nom_type,
    tc.couleur_affichage,
    dc.date_debut,
    dc.date_fin,
    dc.nombre_jours,
    dc.statut
FROM demandes_conges dc
JOIN utilisateurs u ON dc.demandeur_id = u.id
JOIN grades g ON u.grade_id = g.id
JOIN unites un ON u.unite_id = un.id
JOIN types_conges tc ON dc.type_conge_id = tc.id
WHERE dc.statut = 'Approuve_final'
AND dc.date_fin >= CURDATE();

-- ===================================================================
-- PROCÉDURES STOCKÉES
-- ===================================================================

-- Procédure pour calculer les jours ouvrables entre deux dates
DELIMITER //
CREATE PROCEDURE CalculerJoursOuvrables(
    IN date_debut DATE,
    IN date_fin DATE,
    OUT jours_ouvrables INT
)
BEGIN
    DECLARE jours_total INT;
    DECLARE weekends INT;
    DECLARE feries INT;
    
    -- Calculer le nombre total de jours
    SET jours_total = DATEDIFF(date_fin, date_debut) + 1;
    
    -- Calculer les weekends (vendredi et samedi en Algérie)
    SET weekends = (
        SELECT COUNT(*)
        FROM (
            SELECT DATE_ADD(date_debut, INTERVAL n DAY) as date_courante
            FROM (
                SELECT a.N + b.N * 10 + c.N * 100 as n
                FROM (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
                CROSS JOIN (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
                CROSS JOIN (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
            ) numbers
            WHERE DATE_ADD(date_debut, INTERVAL n DAY) <= date_fin
        ) dates
        WHERE DAYOFWEEK(date_courante) IN (6, 7) -- Vendredi et Samedi
    );
    
    -- Calculer les jours fériés
    SELECT COUNT(*) INTO feries
    FROM jours_feries
    WHERE date_ferie BETWEEN date_debut AND date_fin
    AND DAYOFWEEK(date_ferie) NOT IN (6, 7); -- Exclure les fériés qui tombent le weekend
    
    SET jours_ouvrables = jours_total - weekends - feries;
END //
DELIMITER ;

-- Procédure pour mettre à jour les jours de congés restants
DELIMITER //
CREATE PROCEDURE MettreAJourCongesRestants(
    IN utilisateur_id INT
)
BEGIN
    DECLARE jours_utilises INT DEFAULT 0;
    DECLARE jours_annuels INT DEFAULT 0;
    
    -- Récupérer les jours de congés annuels
    SELECT jours_conges_annuels INTO jours_annuels
    FROM utilisateurs
    WHERE id = utilisateur_id;
    
    -- Calculer les jours utilisés cette année
    SELECT COALESCE(SUM(nombre_jours), 0) INTO jours_utilises
    FROM demandes_conges dc
    JOIN types_conges tc ON dc.type_conge_id = tc.id
    WHERE dc.demandeur_id = utilisateur_id
    AND dc.statut = 'Approuve_final'
    AND tc.deductible_conges_annuels = TRUE
    AND YEAR(dc.date_debut) = YEAR(CURDATE());
    
    -- Mettre à jour les jours restants
    UPDATE utilisateurs
    SET jours_conges_restants = jours_annuels - jours_utilises
    WHERE id = utilisateur_id;
END //
DELIMITER ;

-- Procédure pour vérifier les conflits de congés
DELIMITER //
CREATE PROCEDURE VerifierConflitsConges(
    IN demandeur_id INT,
    IN date_debut DATE,
    IN date_fin DATE,
    OUT conflits_count INT
)
BEGIN
    SELECT COUNT(*) INTO conflits_count
    FROM demandes_conges
    WHERE demandeur_id = demandeur_id
    AND statut IN ('Approuve_final', 'Approuve_niveau_1', 'Approuve_niveau_2')
    AND (
        (date_debut BETWEEN date_debut AND date_fin) OR
        (date_fin BETWEEN date_debut AND date_fin) OR
        (date_debut <= date_debut AND date_fin >= date_fin)
    );
END //
DELIMITER ;

-- Procédure pour générer des notifications automatiques
DELIMITER //
CREATE PROCEDURE GenererNotificationRetour()
BEGIN
    INSERT INTO notifications (destinataire_id, titre, message, type_notification, demande_id)
    SELECT 
        dc.demandeur_id,
        'Rappel de retour de congé',
        CONCAT('Votre congé se termine le ', DATE_FORMAT(dc.date_fin, '%d/%m/%Y'), '. N''oubliez pas de reprendre le service.'),
        'Rappel',
        dc.id
    FROM demandes_conges dc
    WHERE dc.statut = 'Approuve_final'
    AND dc.date_fin = DATE_ADD(CURDATE(), INTERVAL 1 DAY)
    AND NOT EXISTS (
        SELECT 1 FROM notifications n 
        WHERE n.demande_id = dc.id 
        AND n.type_notification = 'Rappel'
        AND DATE(n.created_at) = CURDATE()
    );
END //
DELIMITER ;

-- ===================================================================
-- TRIGGERS POUR AUTOMATISER CERTAINES ACTIONS
-- ===================================================================

-- Trigger pour mettre à jour automatiquement les congés restants
DELIMITER //
CREATE TRIGGER after_demande_approuvee
AFTER UPDATE ON demandes_conges
FOR EACH ROW
BEGIN
    IF NEW.statut = 'Approuve_final' AND OLD.statut != 'Approuve_final' THEN
        CALL MettreAJourCongesRestants(NEW.demandeur_id);
        
        -- Créer une notification
        INSERT INTO notifications (destinataire_id, titre, message, type_notification, demande_id)
        VALUES (
            NEW.demandeur_id,
            'Demande de congé approuvée',
            CONCAT('Votre demande de congé du ', DATE_FORMAT(NEW.date_debut, '%d/%m/%Y'), 
                   ' au ', DATE_FORMAT(NEW.date_fin, '%d/%m/%Y'), ' a été approuvée.'),
            'Info',
            NEW.id
        );
    END IF;
    
    IF NEW.statut = 'Refuse' AND OLD.statut != 'Refuse' THEN
        -- Créer une notification de refus
        INSERT INTO notifications (destinataire_id, titre, message, type_notification, demande_id)
        VALUES (
            NEW.demandeur_id,
            'Demande de congé refusée',
            CONCAT('Votre demande de congé du ', DATE_FORMAT(NEW.date_debut, '%d/%m/%Y'), 
                   ' au ', DATE_FORMAT(NEW.date_fin, '%d/%m/%Y'), ' a été refusée. Motif: ', 
                   COALESCE(NEW.commentaire_refus, 'Non spécifié')),
            'Alerte',
            NEW.id
        );
    END IF;
END //
DELIMITER ;

-- Trigger pour enregistrer l'historique
DELIMITER //
CREATE TRIGGER after_demande_insert
AFTER INSERT ON demandes_conges
FOR EACH ROW
BEGIN
    INSERT INTO historique_conges (utilisateur_id, demande_id, action, details, effectue_par_id)
    VALUES (
        NEW.demandeur_id,
        NEW.id,
        'Soumission',
        CONCAT('Demande de ', (SELECT nom_type FROM types_conges WHERE id = NEW.type_conge_id), 
               ' du ', DATE_FORMAT(NEW.date_debut, '%d/%m/%Y'), 
               ' au ', DATE_FORMAT(NEW.date_fin, '%d/%m/%Y')),
        NEW.demandeur_id
    );
END //
DELIMITER ;
