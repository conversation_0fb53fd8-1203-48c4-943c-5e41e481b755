# ===================================================================
# متطلبات نظام إدارة الإجازات العسكرية - Python
# ===================================================================

# Flask Framework
Flask==3.0.0
Flask-CORS==4.0.0

# Security
Werkzeug==3.0.1
itsdangerous==2.1.2

# JSON Web Tokens (optional)
PyJWT==2.8.0

# Database (optional - for future MySQL integration)
PyMySQL==1.1.0
SQLAlchemy==2.0.23

# File handling
Pillow==10.1.0

# Date and time
python-dateutil==2.8.2

# Environment variables
python-dotenv==1.0.0

# HTTP requests (for testing)
requests==2.31.0

# Development tools
watchdog==3.0.0
