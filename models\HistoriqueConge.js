// ===================================================================
// MODÈLE HISTORIQUE CONGÉ - HISTORIQUE DES ACTIONS SUR LES CONGÉS
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const HistoriqueConge = sequelize.define('HistoriqueConge', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    utilisateur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    demande_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'demandes_conges',
        key: 'id'
      }
    },
    action: {
      type: DataTypes.ENUM('Soumission', 'Approbation', 'Refus', 'Modification', 'Annulation'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['Soumission', 'Approbation', 'Refus', 'Modification', 'Annulation']],
          msg: 'Type d\'action invalide'
        }
      }
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 2000],
          msg: 'Les détails ne peuvent pas dépasser 2000 caractères'
        }
      }
    },
    effectue_par_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    date_action: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'historique_conges',
    timestamps: false,
    indexes: [
      {
        fields: ['utilisateur_id']
      },
      {
        fields: ['demande_id']
      },
      {
        fields: ['effectue_par_id']
      },
      {
        fields: ['action']
      },
      {
        fields: ['date_action']
      }
    ],
    hooks: {
      beforeValidate: (historique) => {
        // Nettoyer les détails
        if (historique.details) {
          historique.details = historique.details.trim();
        }
      }
    }
  });

  // Méthodes d'instance
  HistoriqueConge.prototype.estSoumission = function() {
    return this.action === 'Soumission';
  };

  HistoriqueConge.prototype.estApprobation = function() {
    return this.action === 'Approbation';
  };

  HistoriqueConge.prototype.estRefus = function() {
    return this.action === 'Refus';
  };

  HistoriqueConge.prototype.estModification = function() {
    return this.action === 'Modification';
  };

  HistoriqueConge.prototype.estAnnulation = function() {
    return this.action === 'Annulation';
  };

  HistoriqueConge.prototype.getActionLibelle = function() {
    const actions = {
      'Soumission': 'Demande soumise',
      'Approbation': 'Demande approuvée',
      'Refus': 'Demande refusée',
      'Modification': 'Demande modifiée',
      'Annulation': 'Demande annulée'
    };
    
    return actions[this.action] || this.action;
  };

  HistoriqueConge.prototype.getActionCouleur = function() {
    const couleurs = {
      'Soumission': '#17a2b8',
      'Approbation': '#28a745',
      'Refus': '#dc3545',
      'Modification': '#ffc107',
      'Annulation': '#6c757d'
    };
    
    return couleurs[this.action] || '#6c757d';
  };

  HistoriqueConge.prototype.getActionIcone = function() {
    const icones = {
      'Soumission': 'fas fa-paper-plane',
      'Approbation': 'fas fa-check-circle',
      'Refus': 'fas fa-times-circle',
      'Modification': 'fas fa-edit',
      'Annulation': 'fas fa-ban'
    };
    
    return icones[this.action] || 'fas fa-info-circle';
  };

  HistoriqueConge.prototype.getTempsEcoule = function() {
    const maintenant = new Date();
    const diffTime = maintenant - this.date_action;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffMinutes > 0) {
      return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      return 'À l\'instant';
    }
  };

  // Méthodes de classe
  HistoriqueConge.enregistrerAction = async function(utilisateurId, demandeId, action, details, effectueParId) {
    return await this.create({
      utilisateur_id: utilisateurId,
      demande_id: demandeId,
      action: action,
      details: details,
      effectue_par_id: effectueParId
    });
  };

  HistoriqueConge.getHistoriqueParUtilisateur = async function(utilisateurId, limite = 50) {
    return await this.findAll({
      where: { utilisateur_id: utilisateurId },
      include: [
        {
          association: 'demande',
          include: ['type_conge']
        },
        {
          association: 'effectue_par',
          include: ['grade']
        }
      ],
      order: [['date_action', 'DESC']],
      limit: limite
    });
  };

  HistoriqueConge.getHistoriqueParDemande = async function(demandeId) {
    return await this.findAll({
      where: { demande_id: demandeId },
      include: [
        {
          association: 'effectue_par',
          include: ['grade', 'unite']
        }
      ],
      order: [['date_action', 'ASC']]
    });
  };

  HistoriqueConge.getHistoriqueParAction = async function(action, limite = 100) {
    return await this.findAll({
      where: { action: action },
      include: [
        {
          association: 'utilisateur',
          include: ['grade', 'unite']
        },
        {
          association: 'demande',
          include: ['type_conge']
        },
        {
          association: 'effectue_par',
          include: ['grade']
        }
      ],
      order: [['date_action', 'DESC']],
      limit: limite
    });
  };

  HistoriqueConge.getHistoriqueParPeriode = async function(dateDebut, dateFin) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        date_action: {
          [Op.between]: [dateDebut, dateFin]
        }
      },
      include: [
        {
          association: 'utilisateur',
          include: ['grade', 'unite']
        },
        {
          association: 'demande',
          include: ['type_conge']
        },
        {
          association: 'effectue_par',
          include: ['grade']
        }
      ],
      order: [['date_action', 'DESC']]
    });
  };

  HistoriqueConge.getStatistiquesActions = async function(annee = null) {
    const where = {};
    
    if (annee) {
      where[sequelize.Sequelize.Op.and] = [
        sequelize.where(sequelize.fn('YEAR', sequelize.col('date_action')), annee)
      ];
    }
    
    return await this.findAll({
      attributes: [
        'action',
        [sequelize.fn('COUNT', sequelize.col('id')), 'nombre'],
        [sequelize.fn('DATE_FORMAT', sequelize.col('date_action'), '%Y-%m'), 'mois']
      ],
      where,
      group: ['action', 'mois'],
      order: [['mois', 'ASC'], ['action', 'ASC']]
    });
  };

  HistoriqueConge.getActiviteRecente = async function(limite = 20) {
    return await this.findAll({
      include: [
        {
          association: 'utilisateur',
          include: ['grade']
        },
        {
          association: 'demande',
          include: ['type_conge']
        },
        {
          association: 'effectue_par',
          include: ['grade']
        }
      ],
      order: [['date_action', 'DESC']],
      limit: limite
    });
  };

  HistoriqueConge.getHistoriqueParEffectuePar = async function(effectueParId, limite = 100) {
    return await this.findAll({
      where: { effectue_par_id: effectueParId },
      include: [
        {
          association: 'utilisateur',
          include: ['grade', 'unite']
        },
        {
          association: 'demande',
          include: ['type_conge']
        }
      ],
      order: [['date_action', 'DESC']],
      limit: limite
    });
  };

  HistoriqueConge.compterActionsParUtilisateur = async function(utilisateurId, action = null) {
    const where = { utilisateur_id: utilisateurId };
    
    if (action) {
      where.action = action;
    }
    
    return await this.count({ where });
  };

  HistoriqueConge.getDerniereAction = async function(demandeId) {
    return await this.findOne({
      where: { demande_id: demandeId },
      include: [
        {
          association: 'effectue_par',
          include: ['grade']
        }
      ],
      order: [['date_action', 'DESC']]
    });
  };

  // Scopes
  HistoriqueConge.addScope('soumissions', {
    where: { action: 'Soumission' }
  });

  HistoriqueConge.addScope('approbations', {
    where: { action: 'Approbation' }
  });

  HistoriqueConge.addScope('refus', {
    where: { action: 'Refus' }
  });

  HistoriqueConge.addScope('modifications', {
    where: { action: 'Modification' }
  });

  HistoriqueConge.addScope('annulations', {
    where: { action: 'Annulation' }
  });

  HistoriqueConge.addScope('recent', {
    order: [['date_action', 'DESC']],
    limit: 50
  });

  HistoriqueConge.addScope('avecDetails', {
    include: [
      {
        association: 'utilisateur',
        include: ['grade', 'unite']
      },
      {
        association: 'demande',
        include: ['type_conge']
      },
      {
        association: 'effectue_par',
        include: ['grade', 'unite']
      }
    ]
  });

  return HistoriqueConge;
};
