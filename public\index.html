<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Gestion des Congés Militaires</title>
    
    <!-- CSS Files -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <link href="css/sidebar.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
</head>
<body class="bg-light">
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- Sidebar Toggle Button -->
            <button class="btn btn-outline-light me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <a class="navbar-brand d-flex align-items-center" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="fw-bold">SGC Militaire</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Search Bar -->
                <div class="navbar-nav me-auto ms-3">
                    <form class="d-flex">
                        <div class="input-group">
                            <input class="form-control form-control-sm" type="search" placeholder="Rechercher..." aria-label="Search">
                            <button class="btn btn-outline-light btn-sm" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- User Menu -->
                <div class="navbar-nav">
                    <!-- Notifications -->
                    <div class="nav-item dropdown me-3">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell fa-lg"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notifications-count">
                                3
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li class="dropdown-header">
                                <i class="fas fa-bell me-2"></i>Notifications
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-semibold">Demande approuvée</div>
                                        <div class="small text-muted">Il y a 2 heures</div>
                                    </div>
                                </div>
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-semibold">Rappel de retour</div>
                                        <div class="small text-muted">Il y a 1 jour</div>
                                    </div>
                                </div>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="#" onclick="showPage('notifications')">
                                Voir toutes les notifications
                            </a></li>
                        </ul>
                    </div>

                    <!-- User Profile -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="images/avatar-default.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                            <span id="user-name">Capitaine KADDOUR</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">
                                <div class="text-center">
                                    <img src="images/avatar-default.png" alt="Avatar" class="rounded-circle mb-2" width="50" height="50">
                                    <div class="fw-semibold">Capitaine KADDOUR</div>
                                    <div class="small text-muted"><EMAIL></div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showPage('profil')">
                                <i class="fas fa-user me-2"></i>Mon Profil
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showPage('parametres')">
                                <i class="fas fa-cog me-2"></i>Paramètres
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-shield-alt"></i>
                <span class="brand-text">SGC Militaire</span>
            </div>
        </div>

        <div class="sidebar-content">
            <!-- User Info -->
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/avatar-default.png" alt="Avatar" class="rounded-circle">
                    <div class="status-indicator online"></div>
                </div>
                <div class="user-info">
                    <div class="user-name" id="sidebar-user-name">Capitaine KADDOUR</div>
                    <div class="user-role" id="sidebar-user-role">1er Régiment d'Infanterie</div>
                    <div class="user-time" id="sidebar-time"></div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showPage('dashboard')" data-page="dashboard">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">Tableau de Bord</span>
                        </a>
                    </li>

                    <li class="nav-divider">
                        <span class="divider-text">Gestion des Congés</span>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('demandes')" data-page="demandes">
                            <i class="fas fa-calendar-plus nav-icon"></i>
                            <span class="nav-text">Mes Demandes</span>
                            <span class="nav-badge">2</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('nouvelle-demande')" data-page="nouvelle-demande">
                            <i class="fas fa-plus-circle nav-icon"></i>
                            <span class="nav-text">Nouvelle Demande</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('approbations')" data-page="approbations">
                            <i class="fas fa-check-circle nav-icon"></i>
                            <span class="nav-text">Approbations</span>
                            <span class="nav-badge bg-warning">5</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('planning')" data-page="planning">
                            <i class="fas fa-calendar nav-icon"></i>
                            <span class="nav-text">Planning</span>
                        </a>
                    </li>

                    <li class="nav-divider">
                        <span class="divider-text">Administration</span>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('utilisateurs')" data-page="utilisateurs">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">Utilisateurs</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('rapports')" data-page="rapports">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">Rapports</span>
                        </a>
                    </li>

                    <li class="nav-divider">
                        <span class="divider-text">Personnel</span>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('profil')" data-page="profil">
                            <i class="fas fa-user nav-icon"></i>
                            <span class="nav-text">Mon Profil</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('notifications')" data-page="notifications">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">Notifications</span>
                            <span class="nav-badge bg-danger">3</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('parametres')" data-page="parametres">
                            <i class="fas fa-cog nav-icon"></i>
                            <span class="nav-text">Paramètres</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="footer-stats">
                <div class="stat-item">
                    <div class="stat-value">23</div>
                    <div class="stat-label">Jours restants</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">2</div>
                    <div class="stat-label">En attente</div>
                </div>
            </div>

            <button class="btn btn-outline-light btn-sm w-100 mt-3" onclick="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
            </button>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page-content active">
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-3">
                            <i class="fas fa-tachometer-alt text-primary me-2"></i>
                            Tableau de Bord
                        </h1>
                        <p class="text-muted">Bienvenue dans votre espace de gestion des congés</p>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Congés Restants
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">23 jours</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Demandes Approuvées
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">12</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            En Attente
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">2</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            Demandes Refusées
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">1</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities and Quick Actions -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-history me-2"></i>Activités Récentes
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="timeline" id="recent-activities">
                                    <!-- Activities will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bolt me-2"></i>Actions Rapides
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="showPage('nouvelle-demande')">
                                        <i class="fas fa-plus me-2"></i>Nouvelle Demande
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="showPage('planning')">
                                        <i class="fas fa-calendar me-2"></i>Voir Planning
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="showPage('demandes')">
                                        <i class="fas fa-list me-2"></i>Mes Demandes
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Upcoming Holidays -->
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-star me-2"></i>Prochains Jours Fériés
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="upcoming-holidays">
                                    <!-- Holidays will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other pages will be loaded here -->
        <div id="demandes-page" class="page-content"></div>
        <div id="nouvelle-demande-page" class="page-content"></div>
        <div id="approbations-page" class="page-content"></div>
        <div id="planning-page" class="page-content"></div>
        <div id="utilisateurs-page" class="page-content"></div>
        <div id="rapports-page" class="page-content"></div>
        <div id="profil-page" class="page-content"></div>
        <div id="notifications-page" class="page-content"></div>
        <div id="parametres-page" class="page-content"></div>
    </main>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/demandes.js"></script>
    <script src="js/utils.js"></script>
</body>
</html>
