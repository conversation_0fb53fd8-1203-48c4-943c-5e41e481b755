// ===================================================================
// ROUTES D'AUTHENTIFICATION
// ===================================================================

const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { Utilisateur, Grade, Unite } = require('../models');
const router = express.Router();

// ===================================================================
// MIDDLEWARE DE VALIDATION
// ===================================================================

const validateLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Email invalide'),
    body('password')
        .isLength({ min: 6 })
        .withMessage('Le mot de passe doit contenir au moins 6 caractères')
];

// ===================================================================
// ROUTES
// ===================================================================

// POST /api/auth/login - Connexion
router.post('/login', validateLogin, async (req, res) => {
    try {
        // Vérifier les erreurs de validation
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Données invalides',
                errors: errors.array()
            });
        }

        const { email, password, remember } = req.body;

        // Chercher l'utilisateur avec ses relations
        const utilisateur = await Utilisateur.scope('withPassword').findOne({
            where: { email: email.toLowerCase() },
            include: [
                {
                    model: Grade,
                    as: 'grade'
                },
                {
                    model: Unite,
                    as: 'unite'
                }
            ]
        });

        if (!utilisateur) {
            return res.status(401).json({
                success: false,
                message: 'Email ou mot de passe incorrect'
            });
        }

        // Vérifier le statut de l'utilisateur
        if (utilisateur.statut !== 'Actif') {
            return res.status(401).json({
                success: false,
                message: 'Compte désactivé'
            });
        }

        // Vérifier le mot de passe
        const motDePasseValide = await bcrypt.compare(password, utilisateur.mot_de_passe);
        if (!motDePasseValide) {
            return res.status(401).json({
                success: false,
                message: 'Email ou mot de passe incorrect'
            });
        }

        // Générer le token JWT
        const tokenPayload = {
            id: utilisateur.id,
            email: utilisateur.email,
            role: utilisateur.role,
            unite_id: utilisateur.unite_id
        };

        const tokenOptions = {
            expiresIn: remember ? process.env.JWT_REFRESH_EXPIRES_IN : process.env.JWT_EXPIRES_IN
        };

        const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, tokenOptions);

        // Mettre à jour la dernière connexion
        await utilisateur.update({
            derniere_connexion: new Date()
        });

        // Préparer les données utilisateur (sans le mot de passe)
        const userData = {
            id: utilisateur.id,
            matricule: utilisateur.matricule,
            nom: utilisateur.nom,
            prenom: utilisateur.prenom,
            email: utilisateur.email,
            role: utilisateur.role,
            statut: utilisateur.statut,
            jours_conges_annuels: utilisateur.jours_conges_annuels,
            jours_conges_restants: utilisateur.jours_conges_restants,
            grade: utilisateur.grade,
            unite: utilisateur.unite,
            derniere_connexion: utilisateur.derniere_connexion
        };

        // Définir le cookie avec le token
        const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: remember ? 7 * 24 * 60 * 60 * 1000 : 8 * 60 * 60 * 1000 // 7 jours ou 8 heures
        };

        res.cookie('token', token, cookieOptions);

        res.json({
            success: true,
            message: 'Connexion réussie',
            data: {
                user: userData,
                token: token
            }
        });

    } catch (error) {
        console.error('Erreur lors de la connexion:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur interne du serveur'
        });
    }
});

// POST /api/auth/logout - Déconnexion
router.post('/logout', (req, res) => {
    try {
        // Supprimer le cookie
        res.clearCookie('token');

        res.json({
            success: true,
            message: 'Déconnexion réussie'
        });

    } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur interne du serveur'
        });
    }
});

// GET /api/auth/me - Informations utilisateur actuel
router.get('/me', authenticateToken, async (req, res) => {
    try {
        const utilisateur = await Utilisateur.findByPk(req.user.id, {
            include: [
                {
                    model: Grade,
                    as: 'grade'
                },
                {
                    model: Unite,
                    as: 'unite'
                }
            ]
        });

        if (!utilisateur) {
            return res.status(404).json({
                success: false,
                message: 'Utilisateur non trouvé'
            });
        }

        res.json({
            success: true,
            data: utilisateur
        });

    } catch (error) {
        console.error('Erreur lors de la récupération des informations utilisateur:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur interne du serveur'
        });
    }
});

// POST /api/auth/refresh - Renouveler le token
router.post('/refresh', authenticateToken, async (req, res) => {
    try {
        const utilisateur = await Utilisateur.findByPk(req.user.id);

        if (!utilisateur || utilisateur.statut !== 'Actif') {
            return res.status(401).json({
                success: false,
                message: 'Utilisateur non autorisé'
            });
        }

        // Générer un nouveau token
        const tokenPayload = {
            id: utilisateur.id,
            email: utilisateur.email,
            role: utilisateur.role,
            unite_id: utilisateur.unite_id
        };

        const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, {
            expiresIn: process.env.JWT_EXPIRES_IN
        });

        res.json({
            success: true,
            data: { token }
        });

    } catch (error) {
        console.error('Erreur lors du renouvellement du token:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur interne du serveur'
        });
    }
});

// ===================================================================
// MIDDLEWARE D'AUTHENTIFICATION
// ===================================================================

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1] || req.cookies.token;

    if (!token) {
        return res.status(401).json({
            success: false,
            message: 'Token d\'accès requis'
        });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({
                success: false,
                message: 'Token invalide ou expiré'
            });
        }

        req.user = user;
        next();
    });
}

// Export du middleware pour utilisation dans d'autres routes
router.authenticateToken = authenticateToken;

module.exports = router;
