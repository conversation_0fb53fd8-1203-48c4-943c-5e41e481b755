// ===================================================================
// MODÈLE PARAMÈTRE SYSTÈME - CONFIGURATION ET PARAMÈTRES GLOBAUX
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const ParametreSysteme = sequelize.define('ParametreSysteme', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    cle_parametre: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'La clé du paramètre ne peut pas être vide'
        },
        len: {
          args: [3, 100],
          msg: 'La clé du paramètre doit contenir entre 3 et 100 caractères'
        },
        is: {
          args: /^[A-Z_][A-Z0-9_]*$/,
          msg: 'La clé du paramètre doit être en majuscules avec des underscores'
        }
      }
    },
    valeur_parametre: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'La valeur du paramètre ne peut pas être vide'
        }
      }
    },
    description_parametre: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'La description ne peut pas dépasser 1000 caractères'
        }
      }
    },
    type_parametre: {
      type: DataTypes.ENUM('String', 'Integer', 'Boolean', 'JSON'),
      defaultValue: 'String',
      validate: {
        isIn: {
          args: [['String', 'Integer', 'Boolean', 'JSON']],
          msg: 'Type de paramètre invalide'
        }
      }
    },
    modifiable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      validate: {
        isBoolean: {
          msg: 'La modifiabilité doit être vraie ou fausse'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'parametres_systeme',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['cle_parametre']
      },
      {
        fields: ['type_parametre']
      },
      {
        fields: ['modifiable']
      }
    ],
    hooks: {
      beforeValidate: (parametre) => {
        // Nettoyer et formater les données
        if (parametre.cle_parametre) {
          parametre.cle_parametre = parametre.cle_parametre.trim().toUpperCase();
        }
        if (parametre.description_parametre) {
          parametre.description_parametre = parametre.description_parametre.trim();
        }
        
        // Valider la valeur selon le type
        if (parametre.type_parametre && parametre.valeur_parametre) {
          parametre.valeur_parametre = parametre.valeur_parametre.toString().trim();
          
          switch (parametre.type_parametre) {
            case 'Integer':
              if (!/^-?\d+$/.test(parametre.valeur_parametre)) {
                throw new Error('La valeur doit être un entier valide');
              }
              break;
            case 'Boolean':
              if (!['true', 'false', '1', '0'].includes(parametre.valeur_parametre.toLowerCase())) {
                throw new Error('La valeur doit être true, false, 1 ou 0');
              }
              break;
            case 'JSON':
              try {
                JSON.parse(parametre.valeur_parametre);
              } catch (e) {
                throw new Error('La valeur doit être un JSON valide');
              }
              break;
          }
        }
      }
    }
  });

  // Méthodes d'instance
  ParametreSysteme.prototype.estModifiable = function() {
    return this.modifiable === true;
  };

  ParametreSysteme.prototype.estString = function() {
    return this.type_parametre === 'String';
  };

  ParametreSysteme.prototype.estInteger = function() {
    return this.type_parametre === 'Integer';
  };

  ParametreSysteme.prototype.estBoolean = function() {
    return this.type_parametre === 'Boolean';
  };

  ParametreSysteme.prototype.estJSON = function() {
    return this.type_parametre === 'JSON';
  };

  ParametreSysteme.prototype.getValeurTypee = function() {
    switch (this.type_parametre) {
      case 'Integer':
        return parseInt(this.valeur_parametre, 10);
      case 'Boolean':
        return ['true', '1'].includes(this.valeur_parametre.toLowerCase());
      case 'JSON':
        try {
          return JSON.parse(this.valeur_parametre);
        } catch (e) {
          return null;
        }
      default:
        return this.valeur_parametre;
    }
  };

  ParametreSysteme.prototype.definirValeur = async function(nouvelleValeur) {
    if (!this.estModifiable()) {
      throw new Error('Ce paramètre n\'est pas modifiable');
    }
    
    // Valider la nouvelle valeur selon le type
    let valeurString = nouvelleValeur.toString().trim();
    
    switch (this.type_parametre) {
      case 'Integer':
        if (!/^-?\d+$/.test(valeurString)) {
          throw new Error('La valeur doit être un entier valide');
        }
        break;
      case 'Boolean':
        if (typeof nouvelleValeur === 'boolean') {
          valeurString = nouvelleValeur.toString();
        } else if (!['true', 'false', '1', '0'].includes(valeurString.toLowerCase())) {
          throw new Error('La valeur doit être true, false, 1 ou 0');
        }
        break;
      case 'JSON':
        if (typeof nouvelleValeur === 'object') {
          valeurString = JSON.stringify(nouvelleValeur);
        } else {
          try {
            JSON.parse(valeurString);
          } catch (e) {
            throw new Error('La valeur doit être un JSON valide');
          }
        }
        break;
    }
    
    this.valeur_parametre = valeurString;
    await this.save();
  };

  // Méthodes de classe
  ParametreSysteme.obtenirValeur = async function(cleParametre, valeurParDefaut = null) {
    const parametre = await this.findOne({
      where: { cle_parametre: cleParametre.toUpperCase() }
    });
    
    if (!parametre) {
      return valeurParDefaut;
    }
    
    return parametre.getValeurTypee();
  };

  ParametreSysteme.definirValeur = async function(cleParametre, valeur, description = null) {
    const cleFormatee = cleParametre.toUpperCase();
    
    const [parametre, created] = await this.findOrCreate({
      where: { cle_parametre: cleFormatee },
      defaults: {
        cle_parametre: cleFormatee,
        valeur_parametre: valeur.toString(),
        description_parametre: description,
        type_parametre: this.detecterType(valeur)
      }
    });
    
    if (!created) {
      await parametre.definirValeur(valeur);
    }
    
    return parametre;
  };

  ParametreSysteme.detecterType = function(valeur) {
    if (typeof valeur === 'boolean') return 'Boolean';
    if (typeof valeur === 'number' && Number.isInteger(valeur)) return 'Integer';
    if (typeof valeur === 'object') return 'JSON';
    return 'String';
  };

  ParametreSysteme.obtenirTousParametres = async function() {
    const parametres = await this.findAll({
      order: [['cle_parametre', 'ASC']]
    });
    
    const result = {};
    parametres.forEach(param => {
      result[param.cle_parametre] = param.getValeurTypee();
    });
    
    return result;
  };

  ParametreSysteme.obtenirParametresModifiables = async function() {
    return await this.findAll({
      where: { modifiable: true },
      order: [['cle_parametre', 'ASC']]
    });
  };

  ParametreSysteme.obtenirParametresParType = async function(type) {
    return await this.findAll({
      where: { type_parametre: type },
      order: [['cle_parametre', 'ASC']]
    });
  };

  ParametreSysteme.rechercherParametres = async function(terme) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        [Op.or]: [
          { cle_parametre: { [Op.like]: `%${terme.toUpperCase()}%` } },
          { description_parametre: { [Op.like]: `%${terme}%` } }
        ]
      },
      order: [['cle_parametre', 'ASC']]
    });
  };

  ParametreSysteme.sauvegarderConfiguration = async function() {
    const parametres = await this.obtenirTousParametres();
    return {
      timestamp: new Date().toISOString(),
      parametres: parametres
    };
  };

  ParametreSysteme.restaurerConfiguration = async function(configuration) {
    if (!configuration.parametres) {
      throw new Error('Configuration invalide');
    }
    
    const resultats = [];
    
    for (const [cle, valeur] of Object.entries(configuration.parametres)) {
      try {
        const parametre = await this.definirValeur(cle, valeur);
        resultats.push({ cle, statut: 'succès', parametre });
      } catch (error) {
        resultats.push({ cle, statut: 'erreur', erreur: error.message });
      }
    }
    
    return resultats;
  };

  ParametreSysteme.reinitialiserParametre = async function(cleParametre) {
    const parametre = await this.findOne({
      where: { cle_parametre: cleParametre.toUpperCase() }
    });
    
    if (!parametre) {
      throw new Error('Paramètre non trouvé');
    }
    
    if (!parametre.estModifiable()) {
      throw new Error('Ce paramètre ne peut pas être réinitialisé');
    }
    
    // Valeurs par défaut selon la clé
    const valeursDefaut = {
      'JOURS_CONGES_ANNUELS_DEFAUT': '30',
      'DELAI_SOUMISSION_CONGE': '7',
      'DUREE_MAX_CONGE_CONTINU': '21',
      'NOTIFICATION_EMAIL_ACTIVE': 'true',
      'NOTIFICATION_SMS_ACTIVE': 'false',
      'DUREE_SESSION_HEURES': '8',
      'BACKUP_AUTOMATIQUE': 'true',
      'LANGUE_DEFAUT': 'fr',
      'FUSEAU_HORAIRE': 'Africa/Algiers'
    };
    
    const valeurDefaut = valeursDefaut[parametre.cle_parametre];
    if (valeurDefaut) {
      await parametre.definirValeur(valeurDefaut);
    }
    
    return parametre;
  };

  // Scopes
  ParametreSysteme.addScope('modifiables', {
    where: { modifiable: true }
  });

  ParametreSysteme.addScope('nonModifiables', {
    where: { modifiable: false }
  });

  ParametreSysteme.addScope('strings', {
    where: { type_parametre: 'String' }
  });

  ParametreSysteme.addScope('integers', {
    where: { type_parametre: 'Integer' }
  });

  ParametreSysteme.addScope('booleans', {
    where: { type_parametre: 'Boolean' }
  });

  ParametreSysteme.addScope('jsons', {
    where: { type_parametre: 'JSON' }
  });

  return ParametreSysteme;
};
