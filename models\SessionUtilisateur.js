// ===================================================================
// MODÈLE SESSION UTILISATEUR - GESTION DES SESSIONS DE CONNEXION
// ===================================================================

const crypto = require('crypto');

module.exports = (sequelize, DataTypes) => {
  const SessionUtilisateur = sequelize.define('SessionUtilisateur', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    utilisateur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    token_session: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Le token de session ne peut pas être vide'
        },
        len: {
          args: [32, 255],
          msg: 'Le token de session doit contenir entre 32 et 255 caractères'
        }
      }
    },
    adresse_ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      validate: {
        isIP: {
          msg: 'Format d\'adresse IP invalide'
        }
      }
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'L\'user agent ne peut pas dépasser 1000 caractères'
        }
      }
    },
    date_creation: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    date_expiration: {
      type: DataTypes.DATE,
      allowNull: false,
      validate: {
        isDate: {
          msg: 'Format de date d\'expiration invalide'
        },
        isAfter: {
          args: new Date().toISOString(),
          msg: 'La date d\'expiration doit être dans le futur'
        }
      }
    },
    actif: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      validate: {
        isBoolean: {
          msg: 'Le statut actif doit être vrai ou faux'
        }
      }
    }
  }, {
    tableName: 'sessions_utilisateurs',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['token_session']
      },
      {
        fields: ['utilisateur_id']
      },
      {
        fields: ['actif']
      },
      {
        fields: ['date_expiration']
      },
      {
        fields: ['adresse_ip']
      }
    ],
    hooks: {
      beforeCreate: (session) => {
        // Générer un token unique si non fourni
        if (!session.token_session) {
          session.token_session = SessionUtilisateur.genererToken();
        }
        
        // Définir la date d'expiration par défaut (8 heures)
        if (!session.date_expiration) {
          const expiration = new Date();
          expiration.setHours(expiration.getHours() + 8);
          session.date_expiration = expiration;
        }
      }
    }
  });

  // Méthodes d'instance
  SessionUtilisateur.prototype.estActive = function() {
    return this.actif === true;
  };

  SessionUtilisateur.prototype.estExpiree = function() {
    return new Date() > this.date_expiration;
  };

  SessionUtilisateur.prototype.estValide = function() {
    return this.estActive() && !this.estExpiree();
  };

  SessionUtilisateur.prototype.desactiver = async function() {
    this.actif = false;
    await this.save();
  };

  SessionUtilisateur.prototype.prolonger = async function(heures = 8) {
    const nouvelleExpiration = new Date();
    nouvelleExpiration.setHours(nouvelleExpiration.getHours() + heures);
    
    this.date_expiration = nouvelleExpiration;
    await this.save();
  };

  SessionUtilisateur.prototype.getDureeRestante = function() {
    if (this.estExpiree()) return 0;
    
    const maintenant = new Date();
    const diffTime = this.date_expiration - maintenant;
    
    return Math.ceil(diffTime / (1000 * 60 * 60)); // en heures
  };

  SessionUtilisateur.prototype.getDureeVie = function() {
    const diffTime = this.date_expiration - this.date_creation;
    return Math.ceil(diffTime / (1000 * 60 * 60)); // en heures
  };

  SessionUtilisateur.prototype.getInfosNavigateur = function() {
    if (!this.user_agent) return null;
    
    const ua = this.user_agent.toLowerCase();
    let navigateur = 'Inconnu';
    let os = 'Inconnu';
    
    // Détection du navigateur
    if (ua.includes('chrome')) navigateur = 'Chrome';
    else if (ua.includes('firefox')) navigateur = 'Firefox';
    else if (ua.includes('safari')) navigateur = 'Safari';
    else if (ua.includes('edge')) navigateur = 'Edge';
    else if (ua.includes('opera')) navigateur = 'Opera';
    
    // Détection de l'OS
    if (ua.includes('windows')) os = 'Windows';
    else if (ua.includes('mac')) os = 'macOS';
    else if (ua.includes('linux')) os = 'Linux';
    else if (ua.includes('android')) os = 'Android';
    else if (ua.includes('ios')) os = 'iOS';
    
    return { navigateur, os };
  };

  // Méthodes de classe
  SessionUtilisateur.genererToken = function() {
    return crypto.randomBytes(32).toString('hex');
  };

  SessionUtilisateur.creerSession = async function(utilisateurId, adresseIp = null, userAgent = null, dureeHeures = 8) {
    const token = this.genererToken();
    const expiration = new Date();
    expiration.setHours(expiration.getHours() + dureeHeures);
    
    return await this.create({
      utilisateur_id: utilisateurId,
      token_session: token,
      adresse_ip: adresseIp,
      user_agent: userAgent,
      date_expiration: expiration
    });
  };

  SessionUtilisateur.verifierSession = async function(token) {
    const session = await this.findOne({
      where: { token_session: token },
      include: [
        {
          association: 'utilisateur',
          include: ['grade', 'unite']
        }
      ]
    });
    
    if (!session) {
      throw new Error('Session invalide');
    }
    
    if (!session.estValide()) {
      await session.desactiver();
      throw new Error('Session expirée');
    }
    
    return session;
  };

  SessionUtilisateur.getSessionsActives = async function(utilisateurId) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        utilisateur_id: utilisateurId,
        actif: true,
        date_expiration: {
          [Op.gt]: new Date()
        }
      },
      order: [['date_creation', 'DESC']]
    });
  };

  SessionUtilisateur.desactiverToutesSessions = async function(utilisateurId) {
    return await this.update(
      { actif: false },
      {
        where: {
          utilisateur_id: utilisateurId,
          actif: true
        }
      }
    );
  };

  SessionUtilisateur.desactiverAutresSessions = async function(utilisateurId, tokenActuel) {
    return await this.update(
      { actif: false },
      {
        where: {
          utilisateur_id: utilisateurId,
          actif: true,
          token_session: {
            [sequelize.Sequelize.Op.ne]: tokenActuel
          }
        }
      }
    );
  };

  SessionUtilisateur.nettoyerSessionsExpirees = async function() {
    const { Op } = sequelize.Sequelize;
    
    return await this.destroy({
      where: {
        [Op.or]: [
          { actif: false },
          {
            date_expiration: {
              [Op.lt]: new Date()
            }
          }
        ]
      }
    });
  };

  SessionUtilisateur.getSessionsParAdresseIP = async function(adresseIp) {
    return await this.findAll({
      where: { adresse_ip: adresseIp },
      include: [
        {
          association: 'utilisateur',
          include: ['grade', 'unite']
        }
      ],
      order: [['date_creation', 'DESC']]
    });
  };

  SessionUtilisateur.getStatistiquesConnexions = async function(utilisateurId) {
    const { Op } = sequelize.Sequelize;
    const maintenant = new Date();
    const il_y_a_24h = new Date(maintenant.getTime() - 24 * 60 * 60 * 1000);
    const il_y_a_7j = new Date(maintenant.getTime() - 7 * 24 * 60 * 60 * 1000);
    const il_y_a_30j = new Date(maintenant.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const [total, actives, derniere24h, derniere7j, derniere30j] = await Promise.all([
      this.count({ where: { utilisateur_id: utilisateurId } }),
      this.count({
        where: {
          utilisateur_id: utilisateurId,
          actif: true,
          date_expiration: { [Op.gt]: maintenant }
        }
      }),
      this.count({
        where: {
          utilisateur_id: utilisateurId,
          date_creation: { [Op.gte]: il_y_a_24h }
        }
      }),
      this.count({
        where: {
          utilisateur_id: utilisateurId,
          date_creation: { [Op.gte]: il_y_a_7j }
        }
      }),
      this.count({
        where: {
          utilisateur_id: utilisateurId,
          date_creation: { [Op.gte]: il_y_a_30j }
        }
      })
    ]);
    
    return {
      total,
      actives,
      derniere24h,
      derniere7j,
      derniere30j
    };
  };

  SessionUtilisateur.getDerniereConnexion = async function(utilisateurId) {
    return await this.findOne({
      where: { utilisateur_id: utilisateurId },
      order: [['date_creation', 'DESC']]
    });
  };

  // Scopes
  SessionUtilisateur.addScope('actives', {
    where: {
      actif: true,
      date_expiration: {
        [sequelize.Sequelize.Op.gt]: new Date()
      }
    }
  });

  SessionUtilisateur.addScope('expirees', {
    where: {
      [sequelize.Sequelize.Op.or]: [
        { actif: false },
        {
          date_expiration: {
            [sequelize.Sequelize.Op.lt]: new Date()
          }
        }
      ]
    }
  });

  SessionUtilisateur.addScope('recentes', {
    order: [['date_creation', 'DESC']],
    limit: 10
  });

  SessionUtilisateur.addScope('avecUtilisateur', {
    include: [
      {
        association: 'utilisateur',
        include: ['grade', 'unite']
      }
    ]
  });

  return SessionUtilisateur;
};
