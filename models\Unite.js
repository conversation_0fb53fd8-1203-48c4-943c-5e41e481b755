// ===================================================================
// MODÈLE UNITE - UNITÉS MILITAIRES
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const Unite = sequelize.define('Unite', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom_unite: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Le nom de l\'unité ne peut pas être vide'
        },
        len: {
          args: [3, 200],
          msg: 'Le nom de l\'unité doit contenir entre 3 et 200 caractères'
        }
      }
    },
    code_unite: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Le code de l\'unité ne peut pas être vide'
        },
        len: {
          args: [2, 20],
          msg: 'Le code de l\'unité doit contenir entre 2 et 20 caractères'
        },
        isAlphanumeric: {
          msg: 'Le code de l\'unité ne peut contenir que des lettres et des chiffres'
        }
      }
    },
    unite_parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'unites',
        key: 'id'
      },
      validate: {
        async notSelfReference(value) {
          if (value === this.id) {
            throw new Error('Une unité ne peut pas être son propre parent');
          }
        }
      }
    },
    commandant_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    localisation: {
      type: DataTypes.STRING(200),
      allowNull: true,
      validate: {
        len: {
          args: [0, 200],
          msg: 'La localisation ne peut pas dépasser 200 caractères'
        }
      }
    },
    telephone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[0-9\-\s\(\)]+$/,
          msg: 'Format de téléphone invalide'
        }
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: {
          msg: 'Format d\'email invalide'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'unites',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['code_unite']
      },
      {
        fields: ['unite_parent_id']
      },
      {
        fields: ['commandant_id']
      },
      {
        fields: ['localisation']
      }
    ],
    hooks: {
      beforeValidate: (unite) => {
        // Nettoyer et formater les données
        if (unite.nom_unite) {
          unite.nom_unite = unite.nom_unite.trim();
        }
        if (unite.code_unite) {
          unite.code_unite = unite.code_unite.trim().toUpperCase();
        }
        if (unite.email) {
          unite.email = unite.email.trim().toLowerCase();
        }
        if (unite.localisation) {
          unite.localisation = unite.localisation.trim();
        }
      },
      beforeCreate: async (unite) => {
        // Vérifier les références circulaires
        if (unite.unite_parent_id) {
          await Unite.verifierReferencesCirculaires(unite.unite_parent_id, unite.id);
        }
      },
      beforeUpdate: async (unite) => {
        // Vérifier les références circulaires lors de la mise à jour
        if (unite.unite_parent_id && unite.changed('unite_parent_id')) {
          await Unite.verifierReferencesCirculaires(unite.unite_parent_id, unite.id);
        }
      }
    }
  });

  // Méthodes d'instance
  Unite.prototype.getNomComplet = function() {
    return `${this.code_unite} - ${this.nom_unite}`;
  };

  Unite.prototype.estSousUnite = function() {
    return this.unite_parent_id !== null;
  };

  Unite.prototype.aDesSubordonnes = async function() {
    const sousUnites = await this.getSous_unites();
    return sousUnites.length > 0;
  };

  Unite.prototype.getNiveauHierarchique = async function() {
    let niveau = 0;
    let uniteActuelle = this;
    
    while (uniteActuelle.unite_parent_id) {
      niveau++;
      uniteActuelle = await Unite.findByPk(uniteActuelle.unite_parent_id);
      if (!uniteActuelle) break;
    }
    
    return niveau;
  };

  Unite.prototype.getCheminHierarchique = async function() {
    const chemin = [this];
    let uniteActuelle = this;
    
    while (uniteActuelle.unite_parent_id) {
      uniteActuelle = await Unite.findByPk(uniteActuelle.unite_parent_id, {
        include: ['unite_parent']
      });
      if (uniteActuelle) {
        chemin.unshift(uniteActuelle);
      } else {
        break;
      }
    }
    
    return chemin;
  };

  // Méthodes de classe
  Unite.verifierReferencesCirculaires = async function(parentId, uniteId) {
    if (!parentId || !uniteId) return;
    
    let uniteActuelle = await Unite.findByPk(parentId);
    const visitees = new Set();
    
    while (uniteActuelle && !visitees.has(uniteActuelle.id)) {
      if (uniteActuelle.id === uniteId) {
        throw new Error('Référence circulaire détectée dans la hiérarchie des unités');
      }
      
      visitees.add(uniteActuelle.id);
      
      if (uniteActuelle.unite_parent_id) {
        uniteActuelle = await Unite.findByPk(uniteActuelle.unite_parent_id);
      } else {
        break;
      }
    }
  };

  Unite.getUnitesRacines = async function() {
    return await this.findAll({
      where: { unite_parent_id: null },
      include: [
        {
          association: 'commandant',
          include: ['grade']
        }
      ],
      order: [['nom_unite', 'ASC']]
    });
  };

  Unite.getArbreHierarchique = async function() {
    const unitesRacines = await this.getUnitesRacines();
    
    const construireArbre = async (unite) => {
      const sousUnites = await unite.getSous_unites({
        include: [
          {
            association: 'commandant',
            include: ['grade']
          }
        ],
        order: [['nom_unite', 'ASC']]
      });
      
      unite.dataValues.enfants = await Promise.all(
        sousUnites.map(sousUnite => construireArbre(sousUnite))
      );
      
      return unite;
    };
    
    return await Promise.all(
      unitesRacines.map(unite => construireArbre(unite))
    );
  };

  Unite.rechercherParNomOuCode = async function(terme) {
    const { Op } = sequelize.Sequelize;
    return await this.findAll({
      where: {
        [Op.or]: [
          { nom_unite: { [Op.like]: `%${terme}%` } },
          { code_unite: { [Op.like]: `%${terme}%` } }
        ]
      },
      include: [
        {
          association: 'commandant',
          include: ['grade']
        },
        {
          association: 'unite_parent'
        }
      ],
      order: [['nom_unite', 'ASC']]
    });
  };

  // Scopes
  Unite.addScope('avecCommandant', {
    include: [
      {
        association: 'commandant',
        include: ['grade']
      }
    ]
  });

  Unite.addScope('avecParent', {
    include: ['unite_parent']
  });

  Unite.addScope('racines', {
    where: { unite_parent_id: null }
  });

  Unite.addScope('sousUnites', {
    where: {
      unite_parent_id: {
        [sequelize.Sequelize.Op.ne]: null
      }
    }
  });

  return Unite;
};
