#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
===================================================================
خادم نظام إدارة الإجازات العسكرية - Python Flask
===================================================================
"""

from flask import Flask, render_template, request, jsonify, send_from_directory, redirect, url_for, session
from flask_cors import CORS
import os
import json
import hashlib
import datetime
from functools import wraps

# إنشاء التطبيق
app = Flask(__name__, 
            static_folder='public', 
            template_folder='public')

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'your-secret-key-here-2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# تفعيل CORS
CORS(app)

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# ===================================================================
# بيانات تجريبية للنظام
# ===================================================================

# المستخدمون التجريبيون
DEMO_USERS = {
    '<EMAIL>': {
        'password': 'admin123',
        'user': {
            'id': 1,
            'matricule': 'ADM001',
            'nom': 'ADMIN',
            'prenom': 'Système',
            'email': '<EMAIL>',
            'role': 'Administrateur',
            'grade': {'nom_grade': 'Administrateur'},
            'unite': {'nom_unite': 'État-Major Général', 'code_unite': 'EMG'},
            'jours_conges_annuels': 30,
            'jours_conges_restants': 30,
            'statut': 'Actif'
        }
    },
    '<EMAIL>': {
        'password': 'user123',
        'user': {
            'id': 2,
            'matricule': 'CPT001',
            'nom': 'KADDOUR',
            'prenom': 'Fatima',
            'email': '<EMAIL>',
            'role': 'Utilisateur',
            'grade': {'nom_grade': 'Capitaine'},
            'unite': {'nom_unite': '1er Régiment d\'Infanterie', 'code_unite': '1RI'},
            'jours_conges_annuels': 30,
            'jours_conges_restants': 23,
            'statut': 'Actif'
        }
    }
}

# طلبات الإجازات التجريبية
DEMO_DEMANDES = [
    {
        'id': 1,
        'demandeur_id': 2,
        'demandeur': 'Capitaine KADDOUR Fatima',
        'type_conge': 'Congé Annuel',
        'date_debut': '2024-12-15',
        'date_fin': '2024-12-29',
        'nombre_jours': 15,
        'statut': 'Approuve_final',
        'motif': 'Congé de fin d\'année',
        'date_soumission': '2024-11-20',
        'adresse_conge': '123 Rue de la Paix, Alger',
        'telephone_conge': '+213 555 123 456'
    },
    {
        'id': 2,
        'demandeur_id': 2,
        'demandeur': 'Capitaine KADDOUR Fatima',
        'type_conge': 'Permission Exceptionnelle',
        'date_debut': '2024-12-10',
        'date_fin': '2024-12-10',
        'nombre_jours': 1,
        'statut': 'En_attente',
        'motif': 'Rendez-vous médical urgent',
        'date_soumission': '2024-12-05',
        'adresse_conge': '',
        'telephone_conge': ''
    }
]

# الإشعارات التجريبية
DEMO_NOTIFICATIONS = [
    {
        'id': 1,
        'destinataire_id': 2,
        'titre': 'Demande approuvée',
        'message': 'Votre demande de congé annuel a été approuvée',
        'type_notification': 'success',
        'lu': False,
        'created_at': '2024-12-01 10:30:00'
    },
    {
        'id': 2,
        'destinataire_id': 2,
        'titre': 'Rappel de retour',
        'message': 'Votre congé se termine dans 3 jours',
        'type_notification': 'warning',
        'lu': False,
        'created_at': '2024-11-30 09:00:00'
    }
]

# ===================================================================
# وظائف مساعدة
# ===================================================================

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """التحقق من كلمة المرور"""
    return hash_password(password) == hashed

def login_required(f):
    """ديكوريتر للتحقق من تسجيل الدخول"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return jsonify({
                'success': False,
                'message': 'Authentification requise'
            }), 401
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session or session['user']['role'] != 'Administrateur':
            return jsonify({
                'success': False,
                'message': 'Accès administrateur requis'
            }), 403
        return f(*args, **kwargs)
    return decorated_function

# ===================================================================
# المسارات الرئيسية
# ===================================================================

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return send_from_directory('public', 'index.html')

@app.route('/login')
def login_page():
    """صفحة تسجيل الدخول"""
    return send_from_directory('public', 'login.html')

# ===================================================================
# API المصادقة
# ===================================================================

@app.route('/api/auth/login', methods=['POST'])
def login():
    """تسجيل الدخول"""
    try:
        data = request.get_json()
        email = data.get('email', '').lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email et mot de passe requis'
            }), 400
        
        # التحقق من المستخدم
        if email in DEMO_USERS:
            user_data = DEMO_USERS[email]
            if user_data['password'] == password:
                # حفظ المستخدم في الجلسة
                session['user'] = user_data['user']
                session['logged_in'] = True
                
                return jsonify({
                    'success': True,
                    'message': 'Connexion réussie',
                    'data': {
                        'user': user_data['user'],
                        'token': 'demo_token_' + str(datetime.datetime.now().timestamp())
                    }
                })
        
        return jsonify({
            'success': False,
            'message': 'Email ou mot de passe incorrect'
        }), 401
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Erreur interne du serveur'
        }), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """تسجيل الخروج"""
    session.clear()
    return jsonify({
        'success': True,
        'message': 'Déconnexion réussie'
    })

@app.route('/api/auth/me', methods=['GET'])
@login_required
def get_current_user():
    """الحصول على بيانات المستخدم الحالي"""
    return jsonify({
        'success': True,
        'data': session['user']
    })

# ===================================================================
# API الطلبات
# ===================================================================

@app.route('/api/demandes', methods=['GET'])
@login_required
def get_demandes():
    """الحصول على طلبات المستخدم"""
    user_id = session['user']['id']
    user_demandes = [d for d in DEMO_DEMANDES if d['demandeur_id'] == user_id]
    
    return jsonify({
        'success': True,
        'data': user_demandes
    })

@app.route('/api/demandes', methods=['POST'])
@login_required
def create_demande():
    """إنشاء طلب جديد"""
    try:
        data = request.get_json()
        user_id = session['user']['id']
        
        # إنشاء طلب جديد
        new_demande = {
            'id': len(DEMO_DEMANDES) + 1,
            'demandeur_id': user_id,
            'demandeur': f"{session['user']['grade']['nom_grade']} {session['user']['prenom']} {session['user']['nom']}",
            'type_conge': data.get('type_conge', ''),
            'date_debut': data.get('date_debut', ''),
            'date_fin': data.get('date_fin', ''),
            'nombre_jours': int(data.get('nombre_jours', 0)),
            'statut': 'En_attente',
            'motif': data.get('motif', ''),
            'date_soumission': datetime.datetime.now().strftime('%Y-%m-%d'),
            'adresse_conge': data.get('adresse_conge', ''),
            'telephone_conge': data.get('telephone_conge', '')
        }
        
        DEMO_DEMANDES.append(new_demande)
        
        return jsonify({
            'success': True,
            'message': 'Demande créée avec succès',
            'data': new_demande
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Erreur lors de la création de la demande'
        }), 500

@app.route('/api/demandes/<int:demande_id>', methods=['GET'])
@login_required
def get_demande(demande_id):
    """الحصول على تفاصيل طلب محدد"""
    demande = next((d for d in DEMO_DEMANDES if d['id'] == demande_id), None)
    
    if not demande:
        return jsonify({
            'success': False,
            'message': 'Demande non trouvée'
        }), 404
    
    return jsonify({
        'success': True,
        'data': demande
    })

# ===================================================================
# API الإشعارات
# ===================================================================

@app.route('/api/notifications', methods=['GET'])
@login_required
def get_notifications():
    """الحصول على إشعارات المستخدم"""
    user_id = session['user']['id']
    user_notifications = [n for n in DEMO_NOTIFICATIONS if n['destinataire_id'] == user_id]
    
    return jsonify({
        'success': True,
        'data': user_notifications
    })

@app.route('/api/notifications/unread', methods=['GET'])
@login_required
def get_unread_notifications():
    """الحصول على الإشعارات غير المقروءة"""
    user_id = session['user']['id']
    unread_notifications = [n for n in DEMO_NOTIFICATIONS 
                           if n['destinataire_id'] == user_id and not n['lu']]
    
    return jsonify({
        'success': True,
        'data': unread_notifications
    })

# ===================================================================
# API لوحة التحكم
# ===================================================================

@app.route('/api/dashboard/stats', methods=['GET'])
@login_required
def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    user_id = session['user']['id']
    user_demandes = [d for d in DEMO_DEMANDES if d['demandeur_id'] == user_id]
    
    stats = {
        'conges_restants': session['user']['jours_conges_restants'],
        'demandes_total': len(user_demandes),
        'demandes_approuvees': len([d for d in user_demandes if d['statut'] == 'Approuve_final']),
        'demandes_en_attente': len([d for d in user_demandes if d['statut'] == 'En_attente']),
        'demandes_refusees': len([d for d in user_demandes if d['statut'] == 'Refuse'])
    }
    
    return jsonify({
        'success': True,
        'data': stats
    })

# ===================================================================
# الملفات الثابتة
# ===================================================================

@app.route('/<path:filename>')
def serve_static(filename):
    """تقديم الملفات الثابتة"""
    return send_from_directory('public', filename)

# ===================================================================
# معالجة الأخطاء
# ===================================================================

@app.errorhandler(404)
def not_found(error):
    """صفحة غير موجودة"""
    return jsonify({
        'success': False,
        'message': 'Page non trouvée'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """خطأ داخلي"""
    return jsonify({
        'success': False,
        'message': 'Erreur interne du serveur'
    }), 500

# ===================================================================
# تشغيل الخادم
# ===================================================================

if __name__ == '__main__':
    print('\n🚀 ===================================')
    print('🛡️  نظام إدارة الإجازات العسكرية')
    print('🚀 ===================================')
    print('📡 بدء تشغيل الخادم...')
    print('🌐 الرابط: http://localhost:5000')
    print('🔐 صفحة الدخول: http://localhost:5000/login')
    print('📊 لوحة التحكم: http://localhost:5000/')
    print('🚀 ===================================')
    print('\n👥 حسابات التجربة:')
    print('👨‍💼 مدير: <EMAIL> / admin123')
    print('👤 مستخدم: <EMAIL> / user123')
    print('🚀 ===================================\n')
    
    # تشغيل الخادم
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
