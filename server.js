// ===================================================================
// SERVEUR PRINCIPAL - SYSTÈME DE GESTION DES CONGÉS MILITAIRES
// ===================================================================

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// Import des routes
const authRoutes = require('./routes/auth');
const demandesRoutes = require('./routes/demandes');
const utilisateursRoutes = require('./routes/utilisateurs');
const notificationsRoutes = require('./routes/notifications');
const dashboardRoutes = require('./routes/dashboard');

// Import de la base de données
const db = require('./models');

// Création de l'application Express
const app = express();
const PORT = process.env.PORT || 3000;

// ===================================================================
// MIDDLEWARE DE SÉCURITÉ
// ===================================================================

// Helmet pour la sécurité des en-têtes HTTP
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"],
            fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"]
        }
    }
}));

// CORS
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limite chaque IP à 100 requêtes par windowMs
    message: 'Trop de requêtes depuis cette IP, veuillez réessayer plus tard.'
});
app.use('/api/', limiter);

// Rate limiting spécial pour l'authentification
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limite à 5 tentatives de connexion par IP
    message: 'Trop de tentatives de connexion, veuillez réessayer plus tard.'
});
app.use('/api/auth/login', authLimiter);

// ===================================================================
// MIDDLEWARE GÉNÉRAL
// ===================================================================

// Compression des réponses
app.use(compression());

// Parsing des cookies
app.use(cookieParser());

// Parsing du body
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging des requêtes
app.use(morgan('combined'));

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'public')));

// ===================================================================
// ROUTES API
// ===================================================================

app.use('/api/auth', authRoutes);
app.use('/api/demandes', demandesRoutes);
app.use('/api/utilisateurs', utilisateursRoutes);
app.use('/api/notifications', notificationsRoutes);
app.use('/api/dashboard', dashboardRoutes);

// ===================================================================
// ROUTES POUR LES PAGES
// ===================================================================

// Route pour la page de connexion
app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// Route pour la page principale
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route pour toutes les autres pages (SPA routing)
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// ===================================================================
// GESTION DES ERREURS
// ===================================================================

// Middleware de gestion des erreurs 404
app.use((req, res, next) => {
    res.status(404).json({
        success: false,
        message: 'Route non trouvée',
        error: 'NOT_FOUND'
    });
});

// Middleware de gestion des erreurs globales
app.use((err, req, res, next) => {
    console.error('Erreur serveur:', err);
    
    // Erreur de validation Sequelize
    if (err.name === 'SequelizeValidationError') {
        return res.status(400).json({
            success: false,
            message: 'Erreur de validation',
            errors: err.errors.map(e => ({
                field: e.path,
                message: e.message
            }))
        });
    }
    
    // Erreur de contrainte unique Sequelize
    if (err.name === 'SequelizeUniqueConstraintError') {
        return res.status(409).json({
            success: false,
            message: 'Cette valeur existe déjà',
            error: 'DUPLICATE_ENTRY'
        });
    }
    
    // Erreur JWT
    if (err.name === 'JsonWebTokenError') {
        return res.status(401).json({
            success: false,
            message: 'Token invalide',
            error: 'INVALID_TOKEN'
        });
    }
    
    // Erreur par défaut
    res.status(err.status || 500).json({
        success: false,
        message: err.message || 'Erreur interne du serveur',
        error: process.env.NODE_ENV === 'development' ? err.stack : 'INTERNAL_ERROR'
    });
});

// ===================================================================
// DÉMARRAGE DU SERVEUR
// ===================================================================

async function startServer() {
    try {
        // Test de la connexion à la base de données
        await db.sequelize.authenticate();
        console.log('✅ Connexion à la base de données établie avec succès.');
        
        // Synchronisation des modèles (en développement uniquement)
        if (process.env.NODE_ENV === 'development') {
            await db.sequelize.sync({ alter: true });
            console.log('✅ Modèles synchronisés avec la base de données.');
        }
        
        // Démarrage du serveur
        app.listen(PORT, () => {
            console.log('\n🚀 ===================================');
            console.log('🛡️  SYSTÈME DE GESTION DES CONGÉS MILITAIRES');
            console.log('🚀 ===================================');
            console.log(`📡 Serveur démarré sur le port ${PORT}`);
            console.log(`🌐 URL: http://localhost:${PORT}`);
            console.log(`🔐 Page de connexion: http://localhost:${PORT}/login`);
            console.log(`📊 Tableau de bord: http://localhost:${PORT}/`);
            console.log('🚀 ===================================\n');
            
            // Afficher les comptes de démonstration
            console.log('👥 COMPTES DE DÉMONSTRATION:');
            console.log('👨‍💼 Administrateur: <EMAIL> / admin123');
            console.log('👤 Utilisateur: <EMAIL> / user123');
            console.log('🚀 ===================================\n');
        });
        
    } catch (error) {
        console.error('❌ Erreur lors du démarrage du serveur:', error);
        process.exit(1);
    }
}

// Gestion propre de l'arrêt du serveur
process.on('SIGTERM', async () => {
    console.log('🛑 Arrêt du serveur en cours...');
    await db.sequelize.close();
    console.log('✅ Connexion à la base de données fermée.');
    process.exit(0);
});

process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur en cours...');
    await db.sequelize.close();
    console.log('✅ Connexion à la base de données fermée.');
    process.exit(0);
});

// Démarrer le serveur
startServer();

module.exports = app;
