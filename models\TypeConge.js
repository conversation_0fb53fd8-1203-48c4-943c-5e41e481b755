// ===================================================================
// MODÈLE TYPE CONGÉ - TYPES DE CONGÉS ET PERMISSIONS
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const TypeConge = sequelize.define('TypeConge', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Le nom du type de congé ne peut pas être vide'
        },
        len: {
          args: [3, 100],
          msg: 'Le nom du type de congé doit contenir entre 3 et 100 caractères'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'La description ne peut pas dépasser 1000 caractères'
        }
      }
    },
    duree_max_jours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        isInt: {
          msg: 'La durée maximale doit être un nombre entier'
        },
        min: {
          args: [1],
          msg: 'La durée maximale doit être d\'au moins 1 jour'
        },
        max: {
          args: [365],
          msg: 'La durée maximale ne peut pas dépasser 365 jours'
        }
      }
    },
    necessite_justificatif: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      validate: {
        isBoolean: {
          msg: 'La nécessité de justificatif doit être vraie ou fausse'
        }
      }
    },
    deductible_conges_annuels: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      validate: {
        isBoolean: {
          msg: 'La déductibilité des congés annuels doit être vraie ou fausse'
        }
      }
    },
    couleur_affichage: {
      type: DataTypes.STRING(7),
      defaultValue: '#007bff',
      validate: {
        is: {
          args: /^#[0-9A-Fa-f]{6}$/,
          msg: 'La couleur doit être au format hexadécimal (#RRGGBB)'
        }
      }
    },
    actif: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      validate: {
        isBoolean: {
          msg: 'Le statut actif doit être vrai ou faux'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'types_conges',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['nom_type']
      },
      {
        fields: ['actif']
      },
      {
        fields: ['necessite_justificatif']
      },
      {
        fields: ['deductible_conges_annuels']
      }
    ],
    hooks: {
      beforeValidate: (typeConge) => {
        // Nettoyer et formater les données
        if (typeConge.nom_type) {
          typeConge.nom_type = typeConge.nom_type.trim();
        }
        if (typeConge.description) {
          typeConge.description = typeConge.description.trim();
        }
        if (typeConge.couleur_affichage) {
          typeConge.couleur_affichage = typeConge.couleur_affichage.trim().toLowerCase();
          // Ajouter # si manquant
          if (!typeConge.couleur_affichage.startsWith('#')) {
            typeConge.couleur_affichage = '#' + typeConge.couleur_affichage;
          }
        }
      }
    }
  });

  // Méthodes d'instance
  TypeConge.prototype.estActif = function() {
    return this.actif === true;
  };

  TypeConge.prototype.necessiteJustificatif = function() {
    return this.necessite_justificatif === true;
  };

  TypeConge.prototype.estDeductibleCongesAnnuels = function() {
    return this.deductible_conges_annuels === true;
  };

  TypeConge.prototype.aDureeMaximale = function() {
    return this.duree_max_jours !== null && this.duree_max_jours > 0;
  };

  TypeConge.prototype.verifierDureeAutorisee = function(nombreJours) {
    if (!this.aDureeMaximale()) {
      return { valide: true };
    }
    
    if (nombreJours > this.duree_max_jours) {
      return {
        valide: false,
        message: `La durée demandée (${nombreJours} jours) dépasse la durée maximale autorisée (${this.duree_max_jours} jours) pour ce type de congé.`
      };
    }
    
    return { valide: true };
  };

  TypeConge.prototype.getInfosAffichage = function() {
    return {
      nom: this.nom_type,
      couleur: this.couleur_affichage,
      description: this.description,
      dureeMax: this.duree_max_jours,
      justificatifRequis: this.necessite_justificatif,
      deductible: this.deductible_conges_annuels
    };
  };

  // Méthodes de classe
  TypeConge.getTypesActifs = async function() {
    return await this.findAll({
      where: { actif: true },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.getTypesAvecJustificatif = async function() {
    return await this.findAll({
      where: { 
        actif: true,
        necessite_justificatif: true 
      },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.getTypesSansJustificatif = async function() {
    return await this.findAll({
      where: { 
        actif: true,
        necessite_justificatif: false 
      },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.getTypesDeductibles = async function() {
    return await this.findAll({
      where: { 
        actif: true,
        deductible_conges_annuels: true 
      },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.getTypesNonDeductibles = async function() {
    return await this.findAll({
      where: { 
        actif: true,
        deductible_conges_annuels: false 
      },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.rechercherParNom = async function(terme) {
    const { Op } = sequelize.Sequelize;
    return await this.findAll({
      where: {
        nom_type: { [Op.like]: `%${terme}%` },
        actif: true
      },
      order: [['nom_type', 'ASC']]
    });
  };

  TypeConge.getStatistiquesUtilisation = async function() {
    const { DemandeConge } = require('./index');
    
    return await this.findAll({
      attributes: [
        'id',
        'nom_type',
        'couleur_affichage',
        [sequelize.fn('COUNT', sequelize.col('demandes.id')), 'nombre_demandes'],
        [sequelize.fn('SUM', 
          sequelize.literal('CASE WHEN demandes.statut = "Approuve_final" THEN demandes.nombre_jours ELSE 0 END')
        ), 'total_jours_approuves'],
        [sequelize.fn('COUNT', 
          sequelize.literal('CASE WHEN demandes.statut = "Approuve_final" THEN 1 END')
        ), 'demandes_approuvees'],
        [sequelize.fn('COUNT', 
          sequelize.literal('CASE WHEN demandes.statut = "Refuse" THEN 1 END')
        ), 'demandes_refusees']
      ],
      include: [{
        model: DemandeConge,
        as: 'demandes',
        attributes: [],
        required: false
      }],
      where: { actif: true },
      group: ['TypeConge.id'],
      order: [['nom_type', 'ASC']]
    });
  };

  // Scopes
  TypeConge.addScope('actifs', {
    where: { actif: true }
  });

  TypeConge.addScope('inactifs', {
    where: { actif: false }
  });

  TypeConge.addScope('avecJustificatif', {
    where: { necessite_justificatif: true }
  });

  TypeConge.addScope('sansJustificatif', {
    where: { necessite_justificatif: false }
  });

  TypeConge.addScope('deductibles', {
    where: { deductible_conges_annuels: true }
  });

  TypeConge.addScope('nonDeductibles', {
    where: { deductible_conges_annuels: false }
  });

  TypeConge.addScope('avecDureeMax', {
    where: {
      duree_max_jours: {
        [sequelize.Sequelize.Op.ne]: null
      }
    }
  });

  TypeConge.addScope('sansDureeMax', {
    where: { duree_max_jours: null }
  });

  return TypeConge;
};
