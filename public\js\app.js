// ===================================================================
// APPLICATION PRINCIPALE - SYSTÈME DE GESTION DES CONGÉS MILITAIRES
// ===================================================================

// Configuration globale
const CONFIG = {
    API_BASE_URL: '/api',
    ITEMS_PER_PAGE: 10,
    SESSION_TIMEOUT: 8 * 60 * 60 * 1000, // 8 heures en millisecondes
    NOTIFICATION_TIMEOUT: 5000,
    DATE_FORMAT: 'DD/MM/YYYY',
    DATETIME_FORMAT: 'DD/MM/YYYY HH:mm',
    SERVER_URL: 'http://localhost:5000'
};

// État global de l'application
const AppState = {
    currentUser: null,
    currentPage: 'dashboard',
    notifications: [],
    demandes: [],
    loading: false
};

// Utilitaires globaux
const Utils = {
    // Formatage des dates
    formatDate: (date, format = CONFIG.DATE_FORMAT) => {
        if (!date) return '';
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        switch (format) {
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD/MM/YYYY HH:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            default:
                return d.toLocaleDateString('fr-FR');
        }
    },

    // Calcul de la différence en jours
    daysDifference: (date1, date2) => {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        const diffTime = Math.abs(d2 - d1);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    },

    // Validation d'email
    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Génération d'ID unique
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Capitalisation
    capitalize: (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    },

    // Nettoyage des chaînes
    sanitizeString: (str) => {
        return str.replace(/[<>]/g, '');
    }
};

// Gestionnaire de notifications
const NotificationManager = {
    show: (message, type = 'info', duration = CONFIG.NOTIFICATION_TIMEOUT) => {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
        `;
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getIcon(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove après la durée spécifiée
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    getIcon: (type) => {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    success: (message) => NotificationManager.show(message, 'success'),
    error: (message) => NotificationManager.show(message, 'danger'),
    warning: (message) => NotificationManager.show(message, 'warning'),
    info: (message) => NotificationManager.show(message, 'info')
};

// Gestionnaire de chargement
const LoadingManager = {
    show: () => {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.classList.add('show');
            AppState.loading = true;
        }
    },

    hide: () => {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.classList.remove('show');
            AppState.loading = false;
        }
    }
};

// Gestionnaire d'API
const ApiManager = {
    request: async (endpoint, options = {}) => {
        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        };

        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            LoadingManager.show();
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API Error:', error);
            NotificationManager.error('Erreur de connexion au serveur');
            throw error;
        } finally {
            LoadingManager.hide();
        }
    },

    get: (endpoint) => ApiManager.request(endpoint),
    
    post: (endpoint, data) => ApiManager.request(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
    }),
    
    put: (endpoint, data) => ApiManager.request(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data)
    }),
    
    delete: (endpoint) => ApiManager.request(endpoint, {
        method: 'DELETE'
    })
};

// Navigation entre les pages
function showPage(pageName) {
    // Masquer toutes les pages
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.remove('active');
    });
    
    // Afficher la page demandée
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
        AppState.currentPage = pageName;
        
        // Mettre à jour la navigation
        updateNavigation(pageName);
        
        // Charger le contenu de la page
        loadPageContent(pageName);
    }
}

// Mise à jour de la navigation active
function updateNavigation(activePage) {
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`[onclick="showPage('${activePage}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// Chargement du contenu des pages
async function loadPageContent(pageName) {
    try {
        switch (pageName) {
            case 'dashboard':
                await loadDashboard();
                break;
            case 'demandes':
                await loadDemandes();
                break;
            case 'nouvelle-demande':
                await loadNouvelleDemande();
                break;
            case 'approbations':
                await loadApprobations();
                break;
            case 'planning':
                await loadPlanning();
                break;
            case 'profil':
                await loadProfil();
                break;
            case 'notifications':
                await loadNotifications();
                break;
            case 'parametres':
                await loadParametres();
                break;
        }
    } catch (error) {
        console.error(`Erreur lors du chargement de la page ${pageName}:`, error);
        NotificationManager.error(`Erreur lors du chargement de la page`);
    }
}

// Déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = 'login.html';
    }
}

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Vérifier l'authentification
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = 'login.html';
            return;
        }

        // Charger les informations utilisateur
        await loadUserInfo();
        
        // Charger le tableau de bord par défaut
        await loadDashboard();
        
        // Initialiser les événements
        initializeEventListeners();
        
        // Démarrer la vérification périodique des notifications
        startNotificationPolling();
        
    } catch (error) {
        console.error('Erreur d\'initialisation:', error);
        NotificationManager.error('Erreur lors de l\'initialisation de l\'application');
    }
});

// Chargement des informations utilisateur
async function loadUserInfo() {
    try {
        const userData = localStorage.getItem('user');
        if (userData) {
            AppState.currentUser = JSON.parse(userData);
            updateUserDisplay();
        } else {
            // Récupérer depuis l'API
            const user = await ApiManager.get('/auth/me');
            AppState.currentUser = user;
            localStorage.setItem('user', JSON.stringify(user));
            updateUserDisplay();
        }
    } catch (error) {
        console.error('Erreur lors du chargement des informations utilisateur:', error);
        logout();
    }
}

// Mise à jour de l'affichage utilisateur
function updateUserDisplay() {
    const userNameElement = document.getElementById('user-name');
    if (userNameElement && AppState.currentUser) {
        const { grade, nom, prenom } = AppState.currentUser;
        userNameElement.textContent = `${grade?.nom_grade || ''} ${prenom} ${nom}`;
    }
}

// Initialisation des écouteurs d'événements
function initializeEventListeners() {
    // Gestion des formulaires
    document.addEventListener('submit', handleFormSubmit);
    
    // Gestion des clics sur les boutons
    document.addEventListener('click', handleButtonClick);
    
    // Gestion du redimensionnement de la fenêtre
    window.addEventListener('resize', Utils.debounce(handleWindowResize, 250));
    
    // Gestion de la fermeture de la page
    window.addEventListener('beforeunload', handleBeforeUnload);
}

// Gestionnaire de soumission de formulaires
function handleFormSubmit(event) {
    const form = event.target;
    if (form.classList.contains('ajax-form')) {
        event.preventDefault();
        handleAjaxForm(form);
    }
}

// Gestionnaire de clics sur les boutons
function handleButtonClick(event) {
    const button = event.target.closest('button');
    if (!button) return;
    
    // Prévenir les double-clics
    if (button.disabled) return;
    
    // Actions spécifiques selon le type de bouton
    if (button.classList.contains('btn-delete')) {
        handleDeleteAction(button);
    } else if (button.classList.contains('btn-approve')) {
        handleApproveAction(button);
    } else if (button.classList.contains('btn-reject')) {
        handleRejectAction(button);
    }
}

// Gestionnaire de redimensionnement
function handleWindowResize() {
    // Ajuster les graphiques si nécessaire
    if (window.charts) {
        Object.values(window.charts).forEach(chart => {
            if (chart.resize) chart.resize();
        });
    }
}

// Gestionnaire avant fermeture
function handleBeforeUnload(event) {
    if (AppState.loading) {
        event.preventDefault();
        event.returnValue = 'Une opération est en cours. Êtes-vous sûr de vouloir quitter ?';
    }
}

// Polling des notifications
function startNotificationPolling() {
    setInterval(async () => {
        try {
            const notifications = await ApiManager.get('/notifications/unread');
            updateNotificationCount(notifications.length);
        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
        }
    }, 30000); // Vérifier toutes les 30 secondes
}

// Mise à jour du compteur de notifications
function updateNotificationCount(count) {
    const badge = document.getElementById('notifications-count');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

// Gestionnaire de formulaires AJAX
async function handleAjaxForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    const action = form.getAttribute('data-action');
    const method = form.getAttribute('data-method') || 'POST';

    try {
        const result = await ApiManager.request(action, {
            method: method,
            body: JSON.stringify(data)
        });

        NotificationManager.success('Opération réussie');

        // Recharger la page actuelle
        await loadPageContent(AppState.currentPage);

        // Fermer le modal si ouvert
        const modal = form.closest('.modal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) bsModal.hide();
        }

    } catch (error) {
        NotificationManager.error('Erreur lors de l\'opération');
    }
}

// Gestionnaire d'actions de suppression
async function handleDeleteAction(button) {
    const itemId = button.getAttribute('data-id');
    const itemType = button.getAttribute('data-type');

    if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
        try {
            await ApiManager.delete(`/${itemType}/${itemId}`);
            NotificationManager.success('Élément supprimé avec succès');
            await loadPageContent(AppState.currentPage);
        } catch (error) {
            NotificationManager.error('Erreur lors de la suppression');
        }
    }
}

// Gestionnaire d'approbation
async function handleApproveAction(button) {
    const demandeId = button.getAttribute('data-id');
    const commentaire = prompt('Commentaire (optionnel):');

    try {
        await ApiManager.post(`/demandes/${demandeId}/approve`, { commentaire });
        NotificationManager.success('Demande approuvée');
        await loadPageContent(AppState.currentPage);
    } catch (error) {
        NotificationManager.error('Erreur lors de l\'approbation');
    }
}

// Gestionnaire de refus
async function handleRejectAction(button) {
    const demandeId = button.getAttribute('data-id');
    const commentaire = prompt('Motif du refus (obligatoire):');

    if (!commentaire || commentaire.trim() === '') {
        NotificationManager.warning('Le motif du refus est obligatoire');
        return;
    }

    try {
        await ApiManager.post(`/demandes/${demandeId}/reject`, { commentaire });
        NotificationManager.success('Demande refusée');
        await loadPageContent(AppState.currentPage);
    } catch (error) {
        NotificationManager.error('Erreur lors du refus');
    }
}

// Export des fonctions globales
window.showPage = showPage;
window.logout = logout;
window.Utils = Utils;
window.NotificationManager = NotificationManager;
window.LoadingManager = LoadingManager;
window.ApiManager = ApiManager;
window.AppState = AppState;
