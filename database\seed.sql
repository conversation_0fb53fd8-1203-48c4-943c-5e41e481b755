-- ===================================================================
-- DONNÉES INITIALES POUR LE SYSTÈME DE GESTION DES CONGÉS MILITAIRES
-- ===================================================================

USE systeme_conges_militaire;

-- ===================================================================
-- INSERTION DES GRADES MILITAIRES
-- ===================================================================
INSERT INTO grades (nom_grade, niveau_hierarchique, type_personnel) VALUES
-- Officiers Généraux
('Général de Corps d''Armée', 10, 'Officier'),
('Général de Division', 9, 'Officier'),
('Général de Brigade', 8, 'Officier'),

-- Officiers Supérieurs
('Colonel', 7, 'Officier'),
('Lieutenant-Colonel', 6, 'Officier'),
('Commandant', 5, 'Officier'),

-- Officiers
('Capitaine', 4, 'Officier'),
('Lieutenant', 3, 'Officier'),
('Sous-Lieutenant', 2, 'Officier'),

-- Sous-Officiers
('Adjudant-Chef', 1, 'Sous-Officier'),
('Adjudant', 0, 'Sous-Officier'),
('Sergent-Chef', -1, 'Sous-Officier'),
('Sergent', -2, 'Sous-Officier'),

-- Militaires du rang
('Caporal-Chef', -3, 'Militaire du rang'),
('Caporal', -4, 'Militaire du rang'),
('Soldat de 1ère Classe', -5, 'Militaire du rang'),
('Soldat de 2ème Classe', -6, 'Militaire du rang');

-- ===================================================================
-- INSERTION DES UNITÉS MILITAIRES
-- ===================================================================
INSERT INTO unites (nom_unite, code_unite, unite_parent_id, localisation, telephone, email) VALUES
-- État-Major
('État-Major Général', 'EMG', NULL, 'Alger', '+213-21-123456', '<EMAIL>'),

-- Régions Militaires
('1ère Région Militaire', '1RM', 1, 'Blida', '+213-25-123456', '<EMAIL>'),
('2ème Région Militaire', '2RM', 1, 'Oran', '+213-41-123456', '<EMAIL>'),
('3ème Région Militaire', '3RM', 1, 'Béchar', '+213-49-123456', '<EMAIL>'),
('4ème Région Militaire', '4RM', 1, 'Ouargla', '+213-29-123456', '<EMAIL>'),
('5ème Région Militaire', '5RM', 1, 'Constantine', '+213-31-123456', '<EMAIL>'),
('6ème Région Militaire', '6RM', 1, 'Tamanrasset', '+213-29-654321', '<EMAIL>'),

-- Unités subordonnées
('1er Régiment d''Infanterie', '1RI', 2, 'Blida', '+213-25-789012', '<EMAIL>'),
('2ème Régiment Blindé', '2RB', 3, 'Oran', '+213-41-789012', '<EMAIL>'),
('École Militaire Polytechnique', 'EMP', 1, 'Bordj El Bahri', '+213-21-987654', '<EMAIL>'),

-- Services
('Service de Santé Militaire', 'SSM', 1, 'Alger', '+213-21-456789', '<EMAIL>'),
('Direction des Ressources Humaines', 'DRH', 1, 'Alger', '+213-21-321654', '<EMAIL>');

-- ===================================================================
-- INSERTION DES TYPES DE CONGÉS
-- ===================================================================
INSERT INTO types_conges (nom_type, description, duree_max_jours, necessite_justificatif, deductible_conges_annuels, couleur_affichage) VALUES
('Congé Annuel', 'Congé annuel réglementaire', 30, FALSE, TRUE, '#28a745'),
('Congé de Maladie', 'Congé pour raisons médicales', 90, TRUE, FALSE, '#dc3545'),
('Congé Exceptionnel', 'Congé pour circonstances exceptionnelles', 7, TRUE, TRUE, '#ffc107'),
('Congé de Maternité', 'Congé de maternité', 98, TRUE, FALSE, '#e83e8c'),
('Congé de Paternité', 'Congé de paternité', 3, TRUE, FALSE, '#6f42c1'),
('Congé de Décès', 'Congé pour décès d''un proche', 5, TRUE, FALSE, '#6c757d'),
('Congé de Mariage', 'Congé pour mariage', 7, TRUE, TRUE, '#fd7e14'),
('Permission de Sortie', 'Permission de courte durée', 3, FALSE, TRUE, '#20c997'),
('Congé de Formation', 'Congé pour formation professionnelle', NULL, TRUE, FALSE, '#17a2b8'),
('Congé Sans Solde', 'Congé sans rémunération', NULL, TRUE, FALSE, '#343a40');

-- ===================================================================
-- INSERTION DES JOURS FÉRIÉS
-- ===================================================================
INSERT INTO jours_feries (nom_ferie, date_ferie, type_ferie, recurrent, description) VALUES
-- Fêtes Nationales
('Nouvel An', '2024-01-01', 'National', TRUE, 'Premier jour de l''année'),
('Fête du Travail', '2024-05-01', 'National', TRUE, 'Journée internationale des travailleurs'),
('Fête de l''Indépendance', '2024-07-05', 'National', TRUE, 'Indépendance de l''Algérie'),
('Révolution du 1er Novembre', '2024-11-01', 'National', TRUE, 'Début de la révolution algérienne'),

-- Fêtes Religieuses (dates variables)
('Aïd el-Fitr', '2024-04-10', 'Religieux', FALSE, 'Fin du Ramadan'),
('Aïd el-Adha', '2024-06-16', 'Religieux', FALSE, 'Fête du sacrifice'),
('Mawlid Nabawi', '2024-09-15', 'Religieux', FALSE, 'Naissance du Prophète'),
('Muharram', '2024-07-07', 'Religieux', FALSE, 'Nouvel an islamique'),

-- Fêtes Militaires
('Journée du Moudjahid', '2024-03-19', 'Militaire', TRUE, 'Hommage aux moudjahidine'),
('Fête de l''Armée', '2024-06-19', 'Militaire', TRUE, 'Fête des forces armées');

-- ===================================================================
-- INSERTION DES PARAMÈTRES SYSTÈME
-- ===================================================================
INSERT INTO parametres_systeme (cle_parametre, valeur_parametre, description_parametre, type_parametre) VALUES
('JOURS_CONGES_ANNUELS_DEFAUT', '30', 'Nombre de jours de congés annuels par défaut', 'Integer'),
('DELAI_SOUMISSION_CONGE', '7', 'Délai minimum en jours pour soumettre une demande', 'Integer'),
('DUREE_MAX_CONGE_CONTINU', '21', 'Durée maximum d''un congé continu en jours', 'Integer'),
('NOTIFICATION_EMAIL_ACTIVE', 'true', 'Activation des notifications par email', 'Boolean'),
('NOTIFICATION_SMS_ACTIVE', 'false', 'Activation des notifications par SMS', 'Boolean'),
('DUREE_SESSION_HEURES', '8', 'Durée de validité d''une session en heures', 'Integer'),
('BACKUP_AUTOMATIQUE', 'true', 'Sauvegarde automatique activée', 'Boolean'),
('LANGUE_DEFAUT', 'fr', 'Langue par défaut du système', 'String'),
('FUSEAU_HORAIRE', 'Africa/Algiers', 'Fuseau horaire du système', 'String'),
('VERSION_SYSTEME', '1.0.0', 'Version actuelle du système', 'String');

-- ===================================================================
-- INSERTION D'UN UTILISATEUR ADMINISTRATEUR PAR DÉFAUT
-- ===================================================================
-- Mot de passe: admin123 (hashé avec bcrypt)
INSERT INTO utilisateurs (
    matricule, nom, prenom, email, mot_de_passe, grade_id, unite_id, 
    date_naissance, date_incorporation, telephone, role, statut
) VALUES (
    'ADM001', 'ADMIN', 'Système', '<EMAIL>', 
    '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqK',
    1, 1, '1980-01-01', '2000-01-01', '+213-21-000000', 
    'Administrateur', 'Actif'
);

-- ===================================================================
-- INSERTION D'UTILISATEURS DE TEST
-- ===================================================================
INSERT INTO utilisateurs (
    matricule, nom, prenom, email, mot_de_passe, grade_id, unite_id, superieur_direct_id,
    date_naissance, date_incorporation, telephone, role, statut
) VALUES 
('COL001', 'BENALI', 'Ahmed', '<EMAIL>', '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqK', 4, 2, 1, '1975-03-15', '1995-09-01', '+213-25-111111', 'Chef_Unite', 'Actif'),
('CPT001', 'KADDOUR', 'Fatima', '<EMAIL>', '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqK', 7, 8, 2, '1985-07-22', '2005-02-15', '+213-25-222222', 'Utilisateur', 'Actif'),
('SGT001', 'MEZIANE', 'Omar', '<EMAIL>', '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqK', 12, 8, 3, '1990-11-10', '2010-06-01', '+213-25-333333', 'Utilisateur', 'Actif'),
('RH001', 'BOUMEDIENE', 'Aicha', '<EMAIL>', '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqK', 6, 12, 1, '1982-05-18', '2002-09-15', '+213-21-444444', 'RH', 'Actif');
