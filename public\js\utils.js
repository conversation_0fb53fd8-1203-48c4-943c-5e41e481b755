// ===================================================================
// UTILITAIRES GÉNÉRAUX
// ===================================================================

// Chargement de la page nouvelle demande
async function loadNouvelleDemande() {
    try {
        const nouvelleDemandePage = document.getElementById('nouvelle-demande-page');
        if (!nouvelleDemandePage) return;
        
        // Charger le contenu HTML de la page
        const response = await fetch('pages/nouvelle-demande.html');
        const html = await response.text();
        nouvelleDemandePage.innerHTML = html;
        
        // Initialiser les événements spécifiques à cette page
        initializeNouvelleDemande();
        
    } catch (error) {
        console.error('Erreur lors du chargement de la nouvelle demande:', error);
        NotificationManager.error('Erreur lors du chargement de la page');
    }
}

// Chargement de la page des approbations
async function loadApprobations() {
    try {
        const approbationsPage = document.getElementById('approbations-page');
        if (!approbationsPage) return;
        
        approbationsPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-3">
                            <i class="fas fa-check-circle text-primary me-2"></i>
                            Demandes à Approuver
                        </h1>
                        <p class="text-muted">Gérez les demandes de congés de votre équipe</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-clock me-2"></i>Demandes en attente
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="approbations-list">
                                    <!-- Liste des approbations -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        await loadApprobationsList();
        
    } catch (error) {
        console.error('Erreur lors du chargement des approbations:', error);
        NotificationManager.error('Erreur lors du chargement des approbations');
    }
}

// Chargement de la liste des approbations
async function loadApprobationsList() {
    try {
        const approbationsContainer = document.getElementById('approbations-list');
        if (!approbationsContainer) return;
        
        // Simuler des demandes en attente d'approbation
        const demandes = [
            {
                id: 1,
                demandeur: 'Sergent MEZIANE Omar',
                type: 'Congé Annuel',
                dateDebut: '2024-12-20',
                dateFin: '2024-12-27',
                nombreJours: 8,
                motif: 'Congé de fin d\'année',
                dateSubmission: '2024-12-01'
            },
            {
                id: 2,
                demandeur: 'Caporal BENALI Ahmed',
                type: 'Permission Exceptionnelle',
                dateDebut: '2024-12-15',
                dateFin: '2024-12-15',
                nombreJours: 1,
                motif: 'Rendez-vous médical urgent',
                dateSubmission: '2024-12-10'
            }
        ];
        
        if (demandes.length === 0) {
            approbationsContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-muted">Aucune demande en attente</h5>
                    <p class="text-muted">Toutes les demandes ont été traitées.</p>
                </div>
            `;
            return;
        }
        
        approbationsContainer.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Demandeur</th>
                            <th>Type</th>
                            <th>Période</th>
                            <th>Durée</th>
                            <th>Soumis le</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${demandes.map(demande => `
                            <tr>
                                <td>
                                    <div class="fw-semibold">${demande.demandeur}</div>
                                    <div class="text-muted small">${demande.motif}</div>
                                </td>
                                <td>${demande.type}</td>
                                <td>
                                    <div>${Utils.formatDate(demande.dateDebut)}</div>
                                    <div class="text-muted small">au ${Utils.formatDate(demande.dateFin)}</div>
                                </td>
                                <td>
                                    <span class="badge bg-info">${demande.nombreJours} jour${demande.nombreJours > 1 ? 's' : ''}</span>
                                </td>
                                <td>${Utils.formatDate(demande.dateSubmission)}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewDemande(${demande.id})" title="Voir détails">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-approve" data-id="${demande.id}" title="Approuver">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-danger btn-reject" data-id="${demande.id}" title="Refuser">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
    } catch (error) {
        console.error('Erreur lors du chargement de la liste des approbations:', error);
    }
}

// Chargement de la page planning
async function loadPlanning() {
    try {
        const planningPage = document.getElementById('planning-page');
        if (!planningPage) return;
        
        planningPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-1">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    Planning des Congés
                                </h1>
                                <p class="text-muted">Vue d'ensemble des congés de l'unité</p>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-primary active" onclick="switchPlanningView('month')">
                                    <i class="fas fa-calendar-alt me-2"></i>Mois
                                </button>
                                <button class="btn btn-outline-primary" onclick="switchPlanningView('week')">
                                    <i class="fas fa-calendar-week me-2"></i>Semaine
                                </button>
                                <button class="btn btn-outline-primary" onclick="switchPlanningView('list')">
                                    <i class="fas fa-list me-2"></i>Liste
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-calendar-check me-2"></i>Décembre 2024
                                    </h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary" onclick="previousMonth()">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="nextMonth()">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="planning-calendar">
                                    <!-- Calendrier sera généré ici -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        generateCalendar();
        
    } catch (error) {
        console.error('Erreur lors du chargement du planning:', error);
        NotificationManager.error('Erreur lors du chargement du planning');
    }
}

// Génération du calendrier
function generateCalendar() {
    const calendarContainer = document.getElementById('planning-calendar');
    if (!calendarContainer) return;
    
    // Simuler un calendrier simple
    const daysOfWeek = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Congés simulés
    const conges = {
        '15': { name: 'Capitaine KADDOUR', type: 'Congé Annuel', color: 'success' },
        '20': { name: 'Sergent MEZIANE', type: 'Permission', color: 'warning' },
        '25': { name: 'Caporal BENALI', type: 'Congé Maladie', color: 'danger' }
    };
    
    let calendarHtml = `
        <div class="calendar-grid">
            <div class="row">
                ${daysOfWeek.map(day => `
                    <div class="col text-center fw-bold text-muted py-2 border-bottom">
                        ${day}
                    </div>
                `).join('')}
            </div>
    `;
    
    // Générer les jours du mois
    const firstDay = new Date(currentYear, currentMonth, 1).getDay();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    
    let dayCount = 1;
    for (let week = 0; week < 6; week++) {
        calendarHtml += '<div class="row">';
        for (let day = 0; day < 7; day++) {
            if (week === 0 && day < firstDay) {
                calendarHtml += '<div class="col calendar-day empty"></div>';
            } else if (dayCount <= daysInMonth) {
                const conge = conges[dayCount.toString()];
                const isToday = dayCount === currentDate.getDate();
                
                calendarHtml += `
                    <div class="col calendar-day ${isToday ? 'today' : ''}" data-day="${dayCount}">
                        <div class="day-number">${dayCount}</div>
                        ${conge ? `
                            <div class="conge-item bg-${conge.color} text-white">
                                <small>${conge.name}</small>
                                <div class="small">${conge.type}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
                dayCount++;
            } else {
                calendarHtml += '<div class="col calendar-day empty"></div>';
            }
        }
        calendarHtml += '</div>';
        
        if (dayCount > daysInMonth) break;
    }
    
    calendarHtml += '</div>';
    
    // Ajouter les styles CSS pour le calendrier
    calendarHtml += `
        <style>
            .calendar-grid .row {
                margin: 0;
            }
            .calendar-day {
                min-height: 100px;
                border: 1px solid #e9ecef;
                padding: 8px;
                position: relative;
            }
            .calendar-day.today {
                background-color: rgba(44, 90, 160, 0.1);
            }
            .calendar-day.empty {
                background-color: #f8f9fa;
            }
            .day-number {
                font-weight: bold;
                margin-bottom: 5px;
            }
            .conge-item {
                font-size: 0.7rem;
                padding: 2px 4px;
                border-radius: 3px;
                margin-bottom: 2px;
            }
        </style>
    `;
    
    calendarContainer.innerHTML = calendarHtml;
}

// Chargement du profil utilisateur
async function loadProfil() {
    try {
        const profilPage = document.getElementById('profil-page');
        if (!profilPage) return;
        
        profilPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-3">
                            <i class="fas fa-user text-primary me-2"></i>
                            Mon Profil
                        </h1>
                        <p class="text-muted">Gérez vos informations personnelles</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <img src="images/avatar-default.png" alt="Avatar" class="rounded-circle mb-3" width="120" height="120">
                                <h5>${AppState.currentUser?.prenom} ${AppState.currentUser?.nom}</h5>
                                <p class="text-muted">${AppState.currentUser?.grade?.nom_grade}</p>
                                <p class="text-muted">${AppState.currentUser?.unite?.nom_unite}</p>
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-camera me-2"></i>Changer la photo
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-edit me-2"></i>Informations personnelles
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="profil-form">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Nom</label>
                                            <input type="text" class="form-control" value="${AppState.currentUser?.nom || ''}" readonly>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Prénom</label>
                                            <input type="text" class="form-control" value="${AppState.currentUser?.prenom || ''}" readonly>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" value="${AppState.currentUser?.email || ''}">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Téléphone</label>
                                            <input type="tel" class="form-control" placeholder="+213 555 123 456">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Adresse</label>
                                        <textarea class="form-control" rows="3" placeholder="Votre adresse..."></textarea>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Sauvegarder
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Erreur lors du chargement du profil:', error);
        NotificationManager.error('Erreur lors du chargement du profil');
    }
}

// Chargement des notifications
async function loadNotifications() {
    try {
        const notificationsPage = document.getElementById('notifications-page');
        if (!notificationsPage) return;
        
        notificationsPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-1">
                                    <i class="fas fa-bell text-primary me-2"></i>
                                    Notifications
                                </h1>
                                <p class="text-muted">Gérez vos notifications et alertes</p>
                            </div>
                            <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                                <i class="fas fa-check-double me-2"></i>Tout marquer comme lu
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div id="notifications-list">
                                    <!-- Notifications seront chargées ici -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        await loadNotificationsList();
        
    } catch (error) {
        console.error('Erreur lors du chargement des notifications:', error);
        NotificationManager.error('Erreur lors du chargement des notifications');
    }
}

// Chargement de la liste des notifications
async function loadNotificationsList() {
    try {
        const notificationsContainer = document.getElementById('notifications-list');
        if (!notificationsContainer) return;
        
        // Simuler des notifications
        const notifications = [
            {
                id: 1,
                type: 'success',
                title: 'Demande approuvée',
                message: 'Votre demande de congé annuel a été approuvée',
                time: 'Il y a 2 heures',
                read: false
            },
            {
                id: 2,
                type: 'warning',
                title: 'Rappel de retour',
                message: 'Votre congé se termine dans 3 jours',
                time: 'Il y a 1 jour',
                read: false
            },
            {
                id: 3,
                type: 'info',
                title: 'Nouvelle demande',
                message: 'Une nouvelle demande nécessite votre approbation',
                time: 'Il y a 2 jours',
                read: true
            }
        ];
        
        if (notifications.length === 0) {
            notificationsContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune notification</h5>
                    <p class="text-muted">Vous n'avez aucune notification pour le moment.</p>
                </div>
            `;
            return;
        }
        
        notificationsContainer.innerHTML = notifications.map(notification => `
            <div class="notification-item ${!notification.read ? 'unread' : ''} p-3 border-bottom">
                <div class="d-flex align-items-start">
                    <div class="notification-icon me-3">
                        <i class="fas fa-${getNotificationIcon(notification.type)} text-${notification.type}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="notification-title fw-semibold">${notification.title}</div>
                        <div class="notification-message text-muted">${notification.message}</div>
                        <div class="notification-time small text-muted">${notification.time}</div>
                    </div>
                    <div class="notification-actions">
                        ${!notification.read ? `
                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(${notification.id})">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(${notification.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
        
        // Ajouter les styles pour les notifications
        const style = document.createElement('style');
        style.textContent = `
            .notification-item.unread {
                background-color: rgba(44, 90, 160, 0.05);
                border-left: 4px solid var(--primary-color);
            }
            .notification-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(0,0,0,0.05);
            }
        `;
        document.head.appendChild(style);
        
    } catch (error) {
        console.error('Erreur lors du chargement de la liste des notifications:', error);
    }
}

// Obtenir l'icône de notification selon le type
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'danger': 'times-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'bell';
}

// Chargement des paramètres
async function loadParametres() {
    try {
        const parametresPage = document.getElementById('parametres-page');
        if (!parametresPage) return;
        
        parametresPage.innerHTML = `
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="h3 mb-3">
                            <i class="fas fa-cog text-primary me-2"></i>
                            Paramètres
                        </h1>
                        <p class="text-muted">Configurez vos préférences</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bell me-2"></i>Notifications
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        Notifications par email
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="browserNotifications">
                                    <label class="form-check-label" for="browserNotifications">
                                        Notifications du navigateur
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="reminderNotifications" checked>
                                    <label class="form-check-label" for="reminderNotifications">
                                        Rappels de retour de congé
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-palette me-2"></i>Apparence
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Thème</label>
                                    <select class="form-select">
                                        <option value="light" selected>Clair</option>
                                        <option value="dark">Sombre</option>
                                        <option value="auto">Automatique</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Langue</label>
                                    <select class="form-select">
                                        <option value="fr" selected>Français</option>
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        NotificationManager.error('Erreur lors du chargement des paramètres');
    }
}

// Fonctions utilitaires pour les actions
function switchPlanningView(view) {
    console.log('Changement de vue planning:', view);
    // Implémenter le changement de vue
}

function previousMonth() {
    console.log('Mois précédent');
    // Implémenter la navigation
}

function nextMonth() {
    console.log('Mois suivant');
    // Implémenter la navigation
}

function markAllAsRead() {
    NotificationManager.success('Toutes les notifications ont été marquées comme lues');
    loadNotificationsList();
}

function markAsRead(id) {
    NotificationManager.success('Notification marquée comme lue');
    loadNotificationsList();
}

function deleteNotification(id) {
    if (confirm('Supprimer cette notification ?')) {
        NotificationManager.success('Notification supprimée');
        loadNotificationsList();
    }
}

function initializeNouvelleDemande() {
    // Initialiser les événements spécifiques à la nouvelle demande
    console.log('Initialisation de la nouvelle demande');
}

// Export des fonctions
window.loadNouvelleDemande = loadNouvelleDemande;
window.loadApprobations = loadApprobations;
window.loadPlanning = loadPlanning;
window.loadProfil = loadProfil;
window.loadNotifications = loadNotifications;
window.loadParametres = loadParametres;
