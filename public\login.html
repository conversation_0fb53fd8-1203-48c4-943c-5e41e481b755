<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Système de Gestion des Congés Militaires</title>
    
    <!-- CSS Files -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    
    <!-- Custom Login Styles -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .login-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            line-height: 1.6;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            height: auto;
        }
        
        .form-floating .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .forgot-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .forgot-link:hover {
            color: #1e3d72;
            text-decoration: underline;
        }
        
        .alert-custom {
            border: none;
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .login-logo {
                font-size: 3rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
</head>
<body>
    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 login-left">
                <div>
                    <div class="login-logo">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h1 class="login-title">SGC Militaire</h1>
                    <p class="login-subtitle">
                        Système de Gestion des Congés et Permissions<br>
                        pour le Personnel Militaire
                    </p>
                    <div class="mt-4">
                        <div class="d-flex justify-content-center gap-4 text-white-50">
                            <div class="text-center">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <div class="small">Gestion des Congés</div>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <div class="small">Workflow d'Approbation</div>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <div class="small">Rapports & Statistiques</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="h-100 d-flex flex-column justify-content-center">
                    <div class="text-center mb-4">
                        <h2 class="h3 text-dark mb-2">Connexion</h2>
                        <p class="text-muted">Accédez à votre espace personnel</p>
                    </div>
                    
                    <!-- Alert Messages -->
                    <div id="alert-container"></div>
                    
                    <!-- Login Form -->
                    <form id="login-form">
                        <div class="form-floating">
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>" required>
                            <label for="email">
                                <i class="fas fa-envelope me-2"></i>Adresse email
                            </label>
                        </div>
                        
                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="Mot de passe" required>
                            <label for="password">
                                <i class="fas fa-lock me-2"></i>Mot de passe
                            </label>
                        </div>
                        
                        <div class="remember-forgot">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Se souvenir de moi
                                </label>
                            </div>
                            <a href="#" class="forgot-link" onclick="showForgotPassword()">
                                Mot de passe oublié ?
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Se connecter
                        </button>
                    </form>
                    
                    <!-- Demo Accounts -->
                    <div class="mt-4">
                        <div class="text-center mb-3">
                            <small class="text-muted">Comptes de démonstration</small>
                        </div>
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="loginDemo('admin')">
                                    <i class="fas fa-user-shield me-1"></i>Administrateur
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="loginDemo('user')">
                                    <i class="fas fa-user me-1"></i>Utilisateur
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            © 2024 Ministère de la Défense Nationale<br>
                            Tous droits réservés
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuration
        const API_BASE_URL = '/api';
        
        // Gestionnaire de notifications
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alert-container');
            const alertHtml = `
                <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
            
            // Auto-remove après 5 secondes
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
        
        // Gestionnaire de soumission du formulaire
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const email = formData.get('email');
            const password = formData.get('password');
            const remember = formData.get('remember');
            
            // Validation côté client
            if (!email || !password) {
                showAlert('Veuillez remplir tous les champs obligatoires');
                return;
            }
            
            // Désactiver le bouton de soumission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connexion...';
            
            try {
                // Simuler l'authentification
                await simulateLogin(email, password, remember);
                
            } catch (error) {
                showAlert(error.message || 'Erreur de connexion');
            } finally {
                // Réactiver le bouton
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
        
        // Simulation de connexion
        async function simulateLogin(email, password, remember) {
            // Simuler un délai de réseau
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Comptes de démonstration
            const demoAccounts = {
                '<EMAIL>': {
                    password: 'admin123',
                    user: {
                        id: 1,
                        nom: 'ADMIN',
                        prenom: 'Système',
                        email: '<EMAIL>',
                        role: 'Administrateur',
                        grade: { nom_grade: 'Administrateur' },
                        unite: { nom_unite: 'État-Major Général' }
                    }
                },
                '<EMAIL>': {
                    password: 'user123',
                    user: {
                        id: 2,
                        nom: 'KADDOUR',
                        prenom: 'Fatima',
                        email: '<EMAIL>',
                        role: 'Utilisateur',
                        grade: { nom_grade: 'Capitaine' },
                        unite: { nom_unite: '1er Régiment d\'Infanterie' }
                    }
                }
            };
            
            const account = demoAccounts[email];
            
            if (!account || account.password !== password) {
                throw new Error('Email ou mot de passe incorrect');
            }
            
            // Générer un token fictif
            const token = 'demo_token_' + Date.now();
            
            // Sauvegarder dans le localStorage
            localStorage.setItem('token', token);
            localStorage.setItem('user', JSON.stringify(account.user));
            
            if (remember) {
                localStorage.setItem('remember_login', 'true');
            }
            
            showAlert('Connexion réussie ! Redirection...', 'success');
            
            // Rediriger vers le tableau de bord
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }
        
        // Connexion avec comptes de démonstration
        function loginDemo(type) {
            const accounts = {
                'admin': { email: '<EMAIL>', password: 'admin123' },
                'user': { email: '<EMAIL>', password: 'user123' }
            };
            
            const account = accounts[type];
            if (account) {
                document.getElementById('email').value = account.email;
                document.getElementById('password').value = account.password;
                
                // Soumettre automatiquement
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        }
        
        // Mot de passe oublié
        function showForgotPassword() {
            showAlert('Fonctionnalité de récupération de mot de passe non implémentée dans cette démonstration.', 'info');
        }
        
        // Vérifier si l'utilisateur est déjà connecté
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (token) {
                window.location.href = 'index.html';
            }
        });
        
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
