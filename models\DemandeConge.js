// ===================================================================
// MODÈLE DEMANDE CONGÉ - DEMANDES DE CONGÉS ET PERMISSIONS
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const DemandeConge = sequelize.define('DemandeConge', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    demandeur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    type_conge_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'types_conges',
        key: 'id'
      }
    },
    date_debut: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: {
          msg: 'Format de date de début invalide'
        },
        isAfter: {
          args: new Date().toISOString().split('T')[0],
          msg: 'La date de début ne peut pas être dans le passé'
        }
      }
    },
    date_fin: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: {
          msg: 'Format de date de fin invalide'
        },
        isAfterDateDebut(value) {
          if (value <= this.date_debut) {
            throw new Error('La date de fin doit être postérieure à la date de début');
          }
        }
      }
    },
    nombre_jours: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        isInt: {
          msg: 'Le nombre de jours doit être un entier'
        },
        min: {
          args: [1],
          msg: 'Le nombre de jours doit être d\'au moins 1'
        },
        max: {
          args: [365],
          msg: 'Le nombre de jours ne peut pas dépasser 365'
        }
      }
    },
    motif: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'Le motif ne peut pas dépasser 1000 caractères'
        }
      }
    },
    adresse_conge: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 500],
          msg: 'L\'adresse de congé ne peut pas dépasser 500 caractères'
        }
      }
    },
    telephone_conge: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[0-9\-\s\(\)]+$/,
          msg: 'Format de téléphone invalide'
        }
      }
    },
    remplacant_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'utilisateurs',
        key: 'id'
      },
      validate: {
        async notSelfReplacement(value) {
          if (value === this.demandeur_id) {
            throw new Error('Un utilisateur ne peut pas être son propre remplaçant');
          }
        }
      }
    },
    document_justificatif: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isUrl: {
          msg: 'L\'URL du document justificatif est invalide'
        }
      }
    },
    statut: {
      type: DataTypes.ENUM('En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2', 'Approuve_final', 'Refuse', 'Annule'),
      defaultValue: 'En_attente',
      validate: {
        isIn: {
          args: [['En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2', 'Approuve_final', 'Refuse', 'Annule']],
          msg: 'Statut invalide'
        }
      }
    },
    commentaire_refus: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 500],
          msg: 'Le commentaire de refus ne peut pas dépasser 500 caractères'
        }
      }
    },
    date_soumission: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    date_traitement: {
      type: DataTypes.DATE,
      allowNull: true
    },
    traite_par_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'demandes_conges',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['demandeur_id']
      },
      {
        fields: ['type_conge_id']
      },
      {
        fields: ['statut']
      },
      {
        fields: ['date_debut', 'date_fin']
      },
      {
        fields: ['date_soumission']
      },
      {
        fields: ['remplacant_id']
      },
      {
        fields: ['traite_par_id']
      }
    ],
    hooks: {
      beforeValidate: (demande) => {
        // Calculer automatiquement le nombre de jours si non fourni
        if (demande.date_debut && demande.date_fin && !demande.nombre_jours) {
          const debut = new Date(demande.date_debut);
          const fin = new Date(demande.date_fin);
          const diffTime = Math.abs(fin - debut);
          demande.nombre_jours = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        }
        
        // Nettoyer les données textuelles
        if (demande.motif) {
          demande.motif = demande.motif.trim();
        }
        if (demande.adresse_conge) {
          demande.adresse_conge = demande.adresse_conge.trim();
        }
        if (demande.commentaire_refus) {
          demande.commentaire_refus = demande.commentaire_refus.trim();
        }
      },
      beforeUpdate: (demande) => {
        // Mettre à jour la date de traitement si le statut change
        if (demande.changed('statut') && 
            ['Approuve_final', 'Refuse', 'Annule'].includes(demande.statut)) {
          demande.date_traitement = new Date();
        }
      }
    }
  });

  // Méthodes d'instance
  DemandeConge.prototype.estEnAttente = function() {
    return this.statut === 'En_attente';
  };

  DemandeConge.prototype.estApprouvee = function() {
    return this.statut === 'Approuve_final';
  };

  DemandeConge.prototype.estRefusee = function() {
    return this.statut === 'Refuse';
  };

  DemandeConge.prototype.estAnnulee = function() {
    return this.statut === 'Annule';
  };

  DemandeConge.prototype.estEnCours = function() {
    return ['En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2'].includes(this.statut);
  };

  DemandeConge.prototype.peutEtreModifiee = function() {
    return ['En_attente'].includes(this.statut);
  };

  DemandeConge.prototype.peutEtreAnnulee = function() {
    return ['En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2'].includes(this.statut);
  };

  DemandeConge.prototype.getDureeEnJours = function() {
    return this.nombre_jours;
  };

  DemandeConge.prototype.getDureeEnSemaines = function() {
    return Math.ceil(this.nombre_jours / 7);
  };

  DemandeConge.prototype.estEnCoursActuellement = function() {
    const maintenant = new Date();
    const debut = new Date(this.date_debut);
    const fin = new Date(this.date_fin);

    return this.estApprouvee() && maintenant >= debut && maintenant <= fin;
  };

  DemandeConge.prototype.estTerminee = function() {
    const maintenant = new Date();
    const fin = new Date(this.date_fin);
    
    return this.estApprouvee() && maintenant > fin;
  };

  DemandeConge.prototype.estAVenir = function() {
    const maintenant = new Date();
    const debut = new Date(this.date_debut);
    
    return this.estApprouvee() && maintenant < debut;
  };

  DemandeConge.prototype.getJoursRestants = function() {
    if (!this.estAVenir()) return 0;
    
    const maintenant = new Date();
    const debut = new Date(this.date_debut);
    const diffTime = debut - maintenant;
    
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  DemandeConge.prototype.getStatutLibelle = function() {
    const statuts = {
      'En_attente': 'En attente',
      'Approuve_niveau_1': 'Approuvé niveau 1',
      'Approuve_niveau_2': 'Approuvé niveau 2',
      'Approuve_final': 'Approuvé',
      'Refuse': 'Refusé',
      'Annule': 'Annulé'
    };
    
    return statuts[this.statut] || this.statut;
  };

  DemandeConge.prototype.getStatutCouleur = function() {
    const couleurs = {
      'En_attente': '#ffc107',
      'Approuve_niveau_1': '#17a2b8',
      'Approuve_niveau_2': '#007bff',
      'Approuve_final': '#28a745',
      'Refuse': '#dc3545',
      'Annule': '#6c757d'
    };
    
    return couleurs[this.statut] || '#6c757d';
  };

  // Méthodes de classe
  DemandeConge.getDemandesEnAttente = async function() {
    return await this.findAll({
      where: { statut: 'En_attente' },
      include: ['demandeur', 'type_conge'],
      order: [['date_soumission', 'ASC']]
    });
  };

  DemandeConge.getDemandesParUtilisateur = async function(utilisateurId, annee = null) {
    const where = { demandeur_id: utilisateurId };
    
    if (annee) {
      where[sequelize.Sequelize.Op.and] = [
        sequelize.where(sequelize.fn('YEAR', sequelize.col('date_debut')), annee)
      ];
    }
    
    return await this.findAll({
      where,
      include: ['type_conge', 'remplacant', 'traite_par'],
      order: [['date_debut', 'DESC']]
    });
  };

  DemandeConge.getDemandesParPeriode = async function(dateDebut, dateFin) {
    const { Op } = sequelize.Sequelize;
    
    return await this.findAll({
      where: {
        [Op.or]: [
          {
            date_debut: {
              [Op.between]: [dateDebut, dateFin]
            }
          },
          {
            date_fin: {
              [Op.between]: [dateDebut, dateFin]
            }
          },
          {
            [Op.and]: [
              { date_debut: { [Op.lte]: dateDebut } },
              { date_fin: { [Op.gte]: dateFin } }
            ]
          }
        ]
      },
      include: ['demandeur', 'type_conge'],
      order: [['date_debut', 'ASC']]
    });
  };

  DemandeConge.verifierConflits = async function(demandeurId, dateDebut, dateFin, demandeId = null) {
    const { Op } = sequelize.Sequelize;
    const where = {
      demandeur_id: demandeurId,
      statut: {
        [Op.in]: ['Approuve_final', 'Approuve_niveau_1', 'Approuve_niveau_2']
      },
      [Op.or]: [
        {
          date_debut: {
            [Op.between]: [dateDebut, dateFin]
          }
        },
        {
          date_fin: {
            [Op.between]: [dateDebut, dateFin]
          }
        },
        {
          [Op.and]: [
            { date_debut: { [Op.lte]: dateDebut } },
            { date_fin: { [Op.gte]: dateFin } }
          ]
        }
      ]
    };
    
    if (demandeId) {
      where.id = { [Op.ne]: demandeId };
    }
    
    return await this.findAll({ where });
  };

  // Scopes
  DemandeConge.addScope('enAttente', {
    where: { statut: 'En_attente' }
  });

  DemandeConge.addScope('approuvees', {
    where: { statut: 'Approuve_final' }
  });

  DemandeConge.addScope('refusees', {
    where: { statut: 'Refuse' }
  });

  DemandeConge.addScope('enCours', {
    where: {
      statut: {
        [sequelize.Sequelize.Op.in]: ['En_attente', 'Approuve_niveau_1', 'Approuve_niveau_2']
      }
    }
  });

  DemandeConge.addScope('avecDetails', {
    include: [
      { association: 'demandeur', include: ['grade', 'unite'] },
      'type_conge',
      'remplacant',
      'traite_par'
    ]
  });

  return DemandeConge;
};
