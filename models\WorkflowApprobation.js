// ===================================================================
// MODÈLE WORKFLOW APPROBATION - PROCESSUS D'APPROBATION DES CONGÉS
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const WorkflowApprobation = sequelize.define('WorkflowApprobation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    demande_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'demandes_conges',
        key: 'id'
      }
    },
    niveau_approbation: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        isInt: {
          msg: 'Le niveau d\'approbation doit être un entier'
        },
        min: {
          args: [1],
          msg: 'Le niveau d\'approbation doit être d\'au moins 1'
        },
        max: {
          args: [5],
          msg: 'Le niveau d\'approbation ne peut pas dépasser 5'
        }
      }
    },
    approbateur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'utilisateurs',
        key: 'id'
      }
    },
    statut: {
      type: DataTypes.ENUM('En_attente', 'Approuve', 'Refuse'),
      defaultValue: 'En_attente',
      validate: {
        isIn: {
          args: [['En_attente', 'Approuve', 'Refuse']],
          msg: 'Statut d\'approbation invalide'
        }
      }
    },
    commentaire: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'Le commentaire ne peut pas dépasser 1000 caractères'
        }
      }
    },
    date_action: {
      type: DataTypes.DATE,
      allowNull: true
    },
    ordre_traitement: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        isInt: {
          msg: 'L\'ordre de traitement doit être un entier'
        },
        min: {
          args: [1],
          msg: 'L\'ordre de traitement doit être d\'au moins 1'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'workflow_approbations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['demande_id']
      },
      {
        fields: ['approbateur_id']
      },
      {
        fields: ['statut']
      },
      {
        fields: ['niveau_approbation']
      },
      {
        fields: ['ordre_traitement']
      },
      {
        unique: true,
        fields: ['demande_id', 'niveau_approbation']
      }
    ],
    hooks: {
      beforeValidate: (workflow) => {
        // Nettoyer le commentaire
        if (workflow.commentaire) {
          workflow.commentaire = workflow.commentaire.trim();
        }
      },
      beforeUpdate: (workflow) => {
        // Mettre à jour la date d'action si le statut change
        if (workflow.changed('statut') && workflow.statut !== 'En_attente') {
          workflow.date_action = new Date();
        }
      }
    }
  });

  // Méthodes d'instance
  WorkflowApprobation.prototype.estEnAttente = function() {
    return this.statut === 'En_attente';
  };

  WorkflowApprobation.prototype.estApprouve = function() {
    return this.statut === 'Approuve';
  };

  WorkflowApprobation.prototype.estRefuse = function() {
    return this.statut === 'Refuse';
  };

  WorkflowApprobation.prototype.estTraite = function() {
    return ['Approuve', 'Refuse'].includes(this.statut);
  };

  WorkflowApprobation.prototype.approuver = async function(commentaire = null) {
    this.statut = 'Approuve';
    this.commentaire = commentaire;
    this.date_action = new Date();
    await this.save();
  };

  WorkflowApprobation.prototype.refuser = async function(commentaire) {
    if (!commentaire || commentaire.trim() === '') {
      throw new Error('Un commentaire est requis pour refuser une demande');
    }
    
    this.statut = 'Refuse';
    this.commentaire = commentaire.trim();
    this.date_action = new Date();
    await this.save();
  };

  WorkflowApprobation.prototype.getDelaiTraitement = function() {
    if (!this.date_action) return null;
    
    const diffTime = this.date_action - this.created_at;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // en jours
  };

  WorkflowApprobation.prototype.getStatutLibelle = function() {
    const statuts = {
      'En_attente': 'En attente',
      'Approuve': 'Approuvé',
      'Refuse': 'Refusé'
    };
    
    return statuts[this.statut] || this.statut;
  };

  WorkflowApprobation.prototype.getStatutCouleur = function() {
    const couleurs = {
      'En_attente': '#ffc107',
      'Approuve': '#28a745',
      'Refuse': '#dc3545'
    };
    
    return couleurs[this.statut] || '#6c757d';
  };

  // Méthodes de classe
  WorkflowApprobation.creerWorkflowPourDemande = async function(demandeId, approbateurs) {
    const workflows = [];
    
    for (let i = 0; i < approbateurs.length; i++) {
      const workflow = await this.create({
        demande_id: demandeId,
        niveau_approbation: i + 1,
        approbateur_id: approbateurs[i].id,
        ordre_traitement: i + 1
      });
      workflows.push(workflow);
    }
    
    return workflows;
  };

  WorkflowApprobation.getWorkflowParDemande = async function(demandeId) {
    return await this.findAll({
      where: { demande_id: demandeId },
      include: [
        {
          association: 'approbateur',
          include: ['grade', 'unite']
        }
      ],
      order: [['ordre_traitement', 'ASC']]
    });
  };

  WorkflowApprobation.getProchainApprobateur = async function(demandeId) {
    return await this.findOne({
      where: {
        demande_id: demandeId,
        statut: 'En_attente'
      },
      include: [
        {
          association: 'approbateur',
          include: ['grade', 'unite']
        }
      ],
      order: [['ordre_traitement', 'ASC']]
    });
  };

  WorkflowApprobation.getDemandesEnAttenteParApprobateur = async function(approvateurId) {
    return await this.findAll({
      where: {
        approbateur_id: approvateurId,
        statut: 'En_attente'
      },
      include: [
        {
          association: 'demande',
          include: [
            {
              association: 'demandeur',
              include: ['grade', 'unite']
            },
            'type_conge'
          ]
        }
      ],
      order: [['created_at', 'ASC']]
    });
  };

  WorkflowApprobation.verifierTousNiveauxApprouves = async function(demandeId) {
    const workflows = await this.findAll({
      where: { demande_id: demandeId },
      order: [['ordre_traitement', 'ASC']]
    });
    
    return workflows.every(w => w.statut === 'Approuve');
  };

  WorkflowApprobation.verifierRefusAUnNiveau = async function(demandeId) {
    const refus = await this.findOne({
      where: {
        demande_id: demandeId,
        statut: 'Refuse'
      }
    });
    
    return refus !== null;
  };

  WorkflowApprobation.getStatistiquesApprobateur = async function(approvateurId, annee = null) {
    const where = { approbateur_id: approvateurId };
    
    if (annee) {
      where[sequelize.Sequelize.Op.and] = [
        sequelize.where(sequelize.fn('YEAR', sequelize.col('created_at')), annee)
      ];
    }
    
    const [total, approuvees, refusees, enAttente] = await Promise.all([
      this.count({ where }),
      this.count({ where: { ...where, statut: 'Approuve' } }),
      this.count({ where: { ...where, statut: 'Refuse' } }),
      this.count({ where: { ...where, statut: 'En_attente' } })
    ]);
    
    return {
      total,
      approuvees,
      refusees,
      enAttente,
      tauxApprobation: total > 0 ? Math.round((approuvees / total) * 100) : 0
    };
  };

  // Scopes
  WorkflowApprobation.addScope('enAttente', {
    where: { statut: 'En_attente' }
  });

  WorkflowApprobation.addScope('approuves', {
    where: { statut: 'Approuve' }
  });

  WorkflowApprobation.addScope('refuses', {
    where: { statut: 'Refuse' }
  });

  WorkflowApprobation.addScope('traites', {
    where: {
      statut: {
        [sequelize.Sequelize.Op.in]: ['Approuve', 'Refuse']
      }
    }
  });

  WorkflowApprobation.addScope('avecDetails', {
    include: [
      {
        association: 'demande',
        include: ['demandeur', 'type_conge']
      },
      {
        association: 'approbateur',
        include: ['grade', 'unite']
      }
    ]
  });

  return WorkflowApprobation;
};
