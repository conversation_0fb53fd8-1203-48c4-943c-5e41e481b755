// ===================================================================
// INDEX DES MODÈLES - CONFIGURATION SEQUELIZE
// ===================================================================

const { Sequelize } = require('sequelize');
const config = require('../config/database');

// Initialisation de Sequelize
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: config.logging,
    timezone: '+01:00', // Fuseau horaire Algérie
    define: {
      timestamps: true,
      underscored: false,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// Import des modèles
const Grade = require('./Grade')(sequelize, Sequelize.DataTypes);
const Unite = require('./Unite')(sequelize, Sequelize.DataTypes);
const Utilisateur = require('./Utilisateur')(sequelize, Sequelize.DataTypes);
const TypeConge = require('./TypeConge')(sequelize, Sequelize.DataTypes);
const DemandeConge = require('./DemandeConge')(sequelize, Sequelize.DataTypes);
const WorkflowApprobation = require('./WorkflowApprobation')(sequelize, Sequelize.DataTypes);
const JourFerie = require('./JourFerie')(sequelize, Sequelize.DataTypes);
const HistoriqueConge = require('./HistoriqueConge')(sequelize, Sequelize.DataTypes);
const Notification = require('./Notification')(sequelize, Sequelize.DataTypes);
const SessionUtilisateur = require('./SessionUtilisateur')(sequelize, Sequelize.DataTypes);
const ParametreSysteme = require('./ParametreSysteme')(sequelize, Sequelize.DataTypes);

// Définition des associations
const db = {
  sequelize,
  Sequelize,
  Grade,
  Unite,
  Utilisateur,
  TypeConge,
  DemandeConge,
  WorkflowApprobation,
  JourFerie,
  HistoriqueConge,
  Notification,
  SessionUtilisateur,
  ParametreSysteme
};

// ===================================================================
// ASSOCIATIONS ENTRE LES MODÈLES
// ===================================================================

// Relations Grade <-> Utilisateur
Grade.hasMany(Utilisateur, { foreignKey: 'grade_id', as: 'utilisateurs' });
Utilisateur.belongsTo(Grade, { foreignKey: 'grade_id', as: 'grade' });

// Relations Unite <-> Utilisateur
Unite.hasMany(Utilisateur, { foreignKey: 'unite_id', as: 'personnel' });
Utilisateur.belongsTo(Unite, { foreignKey: 'unite_id', as: 'unite' });

// Relations hiérarchiques Unite
Unite.hasMany(Unite, { foreignKey: 'unite_parent_id', as: 'sous_unites' });
Unite.belongsTo(Unite, { foreignKey: 'unite_parent_id', as: 'unite_parent' });

// Commandant d'unité
Unite.belongsTo(Utilisateur, { foreignKey: 'commandant_id', as: 'commandant' });
Utilisateur.hasMany(Unite, { foreignKey: 'commandant_id', as: 'unites_commandees' });

// Relations hiérarchiques Utilisateur
Utilisateur.hasMany(Utilisateur, { foreignKey: 'superieur_direct_id', as: 'subordonnes' });
Utilisateur.belongsTo(Utilisateur, { foreignKey: 'superieur_direct_id', as: 'superieur_direct' });

// Relations TypeConge <-> DemandeConge
TypeConge.hasMany(DemandeConge, { foreignKey: 'type_conge_id', as: 'demandes' });
DemandeConge.belongsTo(TypeConge, { foreignKey: 'type_conge_id', as: 'type_conge' });

// Relations Utilisateur <-> DemandeConge
Utilisateur.hasMany(DemandeConge, { foreignKey: 'demandeur_id', as: 'demandes_conges' });
DemandeConge.belongsTo(Utilisateur, { foreignKey: 'demandeur_id', as: 'demandeur' });

// Remplaçant
Utilisateur.hasMany(DemandeConge, { foreignKey: 'remplacant_id', as: 'remplacements' });
DemandeConge.belongsTo(Utilisateur, { foreignKey: 'remplacant_id', as: 'remplacant' });

// Traité par
Utilisateur.hasMany(DemandeConge, { foreignKey: 'traite_par_id', as: 'demandes_traitees' });
DemandeConge.belongsTo(Utilisateur, { foreignKey: 'traite_par_id', as: 'traite_par' });

// Relations DemandeConge <-> WorkflowApprobation
DemandeConge.hasMany(WorkflowApprobation, { foreignKey: 'demande_id', as: 'approbations' });
WorkflowApprobation.belongsTo(DemandeConge, { foreignKey: 'demande_id', as: 'demande' });

// Relations Utilisateur <-> WorkflowApprobation
Utilisateur.hasMany(WorkflowApprobation, { foreignKey: 'approbateur_id', as: 'approbations_donnees' });
WorkflowApprobation.belongsTo(Utilisateur, { foreignKey: 'approbateur_id', as: 'approbateur' });

// Relations HistoriqueConge
Utilisateur.hasMany(HistoriqueConge, { foreignKey: 'utilisateur_id', as: 'historique_conges' });
HistoriqueConge.belongsTo(Utilisateur, { foreignKey: 'utilisateur_id', as: 'utilisateur' });

DemandeConge.hasMany(HistoriqueConge, { foreignKey: 'demande_id', as: 'historique' });
HistoriqueConge.belongsTo(DemandeConge, { foreignKey: 'demande_id', as: 'demande' });

Utilisateur.hasMany(HistoriqueConge, { foreignKey: 'effectue_par_id', as: 'actions_effectuees' });
HistoriqueConge.belongsTo(Utilisateur, { foreignKey: 'effectue_par_id', as: 'effectue_par' });

// Relations Notification
Utilisateur.hasMany(Notification, { foreignKey: 'destinataire_id', as: 'notifications' });
Notification.belongsTo(Utilisateur, { foreignKey: 'destinataire_id', as: 'destinataire' });

DemandeConge.hasMany(Notification, { foreignKey: 'demande_id', as: 'notifications' });
Notification.belongsTo(DemandeConge, { foreignKey: 'demande_id', as: 'demande' });

// Relations SessionUtilisateur
Utilisateur.hasMany(SessionUtilisateur, { foreignKey: 'utilisateur_id', as: 'sessions' });
SessionUtilisateur.belongsTo(Utilisateur, { foreignKey: 'utilisateur_id', as: 'utilisateur' });

module.exports = db;
