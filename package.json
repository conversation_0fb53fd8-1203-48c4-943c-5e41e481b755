{"name": "systeme-conges-militaire", "version": "1.0.0", "description": "Système de Gestion des Congés et Permissions Militaires", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["military", "leave", "management", "nodejs", "express", "mysql"], "author": "SGC Militaire Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}