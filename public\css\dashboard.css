/* ===================================================================
   STYLES SPÉCIFIQUES AU TABLEAU DE BORD
   =================================================================== */

/* Dashboard Layout */
.dashboard-container {
    padding: 20px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 10px;
}

.dashboard-subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

/* Statistics Cards */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.stats-card.success::before {
    background: var(--success-color);
}

.stats-card.warning::before {
    background: var(--warning-color);
}

.stats-card.danger::before {
    background: var(--danger-color);
}

.stats-card.info::before {
    background: var(--info-color);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-card .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 15px;
}

.stats-card .stats-icon.primary {
    background: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
}

.stats-card .stats-icon.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.stats-card .stats-icon.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stats-card .stats-icon.danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-card .stats-change {
    font-size: 0.85rem;
    margin-top: 10px;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-change.negative {
    color: var(--danger-color);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.quick-actions .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.quick-actions .section-title i {
    margin-right: 10px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 15px 20px;
    margin-bottom: 10px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    background: rgba(44, 90, 160, 0.05);
    color: var(--primary-color);
    transform: translateX(5px);
}

.quick-action-btn i {
    margin-right: 12px;
    font-size: 1.1rem;
}

/* Recent Activities */
.recent-activities {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.activity-icon.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.activity-icon.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.activity-icon.danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.activity-icon.info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.activity-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.activity-time {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Upcoming Holidays */
.upcoming-holidays {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.holiday-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.holiday-item:last-child {
    border-bottom: none;
}

.holiday-date {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: var(--primary-color);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.holiday-date .day {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

.holiday-date .month {
    font-size: 0.7rem;
    text-transform: uppercase;
    line-height: 1;
}

.holiday-info {
    flex: 1;
}

.holiday-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 2px;
}

.holiday-type {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* Progress Bars */
.progress-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar-custom {
    height: 100%;
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-bar-custom.primary {
    background: linear-gradient(90deg, var(--primary-color), #4a90e2);
}

.progress-bar-custom.success {
    background: linear-gradient(90deg, var(--success-color), #5cb85c);
}

.progress-bar-custom.warning {
    background: linear-gradient(90deg, var(--warning-color), #f0ad4e);
}

/* Charts Container */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    margin-bottom: 20px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.chart-filters {
    display: flex;
    gap: 10px;
}

.chart-filter-btn {
    padding: 5px 15px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    background: white;
    color: var(--secondary-color);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-filter-btn.active,
.chart-filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .quick-action-btn {
        padding: 12px 15px;
    }
    
    .activity-item {
        padding: 12px 0;
    }
    
    .holiday-date {
        width: 45px;
        height: 45px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .chart-filters {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stats-card .stats-number {
        font-size: 1.8rem;
    }
    
    .activity-icon {
        width: 35px;
        height: 35px;
    }
    
    .holiday-date {
        width: 40px;
        height: 40px;
    }
    
    .holiday-date .day {
        font-size: 1rem;
    }
    
    .holiday-date .month {
        font-size: 0.6rem;
    }
}
