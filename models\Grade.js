// ===================================================================
// MODÈLE GRADE - GRADES MILITAIRES
// ===================================================================

module.exports = (sequelize, DataTypes) => {
  const Grade = sequelize.define('Grade', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom_grade: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Le nom du grade ne peut pas être vide'
        },
        len: {
          args: [2, 100],
          msg: 'Le nom du grade doit contenir entre 2 et 100 caractères'
        }
      }
    },
    niveau_hierarchique: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        isInt: {
          msg: 'Le niveau hiérarchique doit être un nombre entier'
        },
        min: {
          args: [-10],
          msg: 'Le niveau hiérarchique ne peut pas être inférieur à -10'
        },
        max: {
          args: [15],
          msg: 'Le niveau hiérarchique ne peut pas être supérieur à 15'
        }
      }
    },
    type_personnel: {
      type: DataTypes.ENUM('Officier', 'Sous-Officier', 'Militaire du rang'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['Officier', 'Sous-Officier', 'Militaire du rang']],
          msg: 'Le type de personnel doit être: Officier, Sous-Officier ou Militaire du rang'
        }
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'grades',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['nom_grade']
      },
      {
        fields: ['niveau_hierarchique']
      },
      {
        fields: ['type_personnel']
      }
    ],
    hooks: {
      beforeValidate: (grade) => {
        // Nettoyer et formater le nom du grade
        if (grade.nom_grade) {
          grade.nom_grade = grade.nom_grade.trim();
        }
      }
    }
  });

  // Méthodes d'instance
  Grade.prototype.estSuperieurA = function(autreGrade) {
    return this.niveau_hierarchique > autreGrade.niveau_hierarchique;
  };

  Grade.prototype.estInferieurA = function(autreGrade) {
    return this.niveau_hierarchique < autreGrade.niveau_hierarchique;
  };

  Grade.prototype.estEgalA = function(autreGrade) {
    return this.niveau_hierarchique === autreGrade.niveau_hierarchique;
  };

  // Méthodes de classe
  Grade.getGradesParType = async function(typePersonnel) {
    return await this.findAll({
      where: { type_personnel: typePersonnel },
      order: [['niveau_hierarchique', 'DESC']]
    });
  };

  Grade.getGradeSuperieur = async function(niveauActuel) {
    return await this.findOne({
      where: {
        niveau_hierarchique: {
          [sequelize.Sequelize.Op.gt]: niveauActuel
        }
      },
      order: [['niveau_hierarchique', 'ASC']]
    });
  };

  Grade.getGradeInferieur = async function(niveauActuel) {
    return await this.findOne({
      where: {
        niveau_hierarchique: {
          [sequelize.Sequelize.Op.lt]: niveauActuel
        }
      },
      order: [['niveau_hierarchique', 'DESC']]
    });
  };

  // Scopes
  Grade.addScope('officiers', {
    where: { type_personnel: 'Officier' },
    order: [['niveau_hierarchique', 'DESC']]
  });

  Grade.addScope('sousOfficiers', {
    where: { type_personnel: 'Sous-Officier' },
    order: [['niveau_hierarchique', 'DESC']]
  });

  Grade.addScope('militairesRang', {
    where: { type_personnel: 'Militaire du rang' },
    order: [['niveau_hierarchique', 'DESC']]
  });

  Grade.addScope('parNiveauDesc', {
    order: [['niveau_hierarchique', 'DESC']]
  });

  Grade.addScope('parNiveauAsc', {
    order: [['niveau_hierarchique', 'ASC']]
  });

  return Grade;
};
